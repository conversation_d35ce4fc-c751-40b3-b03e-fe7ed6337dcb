# 强对流试题管理页面修改任务

## 任务描述
根据用户需求，对强对流试题管理页面进行以下修改：
1. 移除启用/禁用功能
2. 移除落区答案配置功能（因为落区答案在落区文件里）
3. 将站点答案配置从弹窗改为直接在当前页面配置
4. 修复stations.push错误

## 修改内容

### 1. 移除启用/禁用功能 ✅
- 从表单中移除"试题状态"选项
- 替换为"预计用时"配置
- 清理相关的数据和验证规则

### 2. 移除落区答案配置功能 ✅
- 移除落区答案配置的HTML模板
- 移除相关的对话框
- 清理相关的JavaScript数据和方法

### 3. 站点答案配置改为页面内配置 ✅
- 移除站点配置对话框
- 直接在页面中嵌入StationAnswerConfig组件
- 简化配置流程

### 4. 修复StationAnswerConfig组件 ✅
- 修复stations.push错误，确保stations始终是数组类型
- 改进数据格式转换，支持对象和数组之间的转换
- 优化UI布局，调整列宽比例
- 改进预警等级选项的描述

## 技术细节

### StationAnswerConfig组件修改
- props.value类型从Array改为Object
- 添加数据格式转换逻辑
- 确保stations数组的类型安全
- 优化updateStations方法，支持对象格式输出

### 表单数据结构调整
- 移除formData.state字段
- 移除formData.standardAreaAnswer字段
- 保留formData.standardStationAnswer作为对象格式

## 测试建议
1. 测试添加站点功能是否正常
2. 测试删除站点功能是否正常
3. 测试站点信息编辑是否能正确保存
4. 验证表单提交时数据格式是否正确 