'use strict'
const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = process.env.VUE_APP_TITLE || 'YF考试系统'
const port = process.env.port || process.env.npm_config_port || 9528
const isProduction = process.env.NODE_ENV === 'production'

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/dev-api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/dev-api': ''
        }
      }
    }
  },
  configureWebpack: config => {
    config.name = name
    
    if (isProduction) {
      // 生产环境优化
      const plugins = []
      
      // Gzip压缩
      plugins.push(
        new CompressionPlugin({
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          deleteOriginalAssets: false,
          compressionOptions: {
            level: 9
          }
        })
      )
      
      // 包分析工具 (可选，用于分析包大小)
      if (process.env.ANALYZE_BUNDLE) {
        plugins.push(new BundleAnalyzerPlugin())
      }
      
      config.plugins.push(...plugins)
      
      // 外部化依赖 (CDN方式)
      config.externals = {
        'vue': 'Vue',
        'element-ui': 'ELEMENT',
        'axios': 'axios'
      }
    }
  },
  chainWebpack: config => {
    // 设置 svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    
    // 预加载优化
    config.plugin('preload').tap(options => {
      options[0] = {
        rel: 'preload',
        include: 'initial',
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/]
      }
      return options
    })
    
    // 预提取优化
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || []
      options[0].fileBlacklist.push(/convection\/.*\.js$/)
      return options
    })
    
    // 代码分割优化
    config.optimization.splitChunks({
      chunks: 'all',
      cacheGroups: {
        // 第三方库分离
        libs: {
          name: 'chunk-libs',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'initial'
        },
        
        // Element UI 分离
        elementUI: {
          name: 'chunk-elementUI',
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
          priority: 20
        },
        
        // 强对流模块分离
        convection: {
          name: 'chunk-convection',
          test: /[\\/]src[\\/](views|components|api)[\\/]convection[\\/]/,
          priority: 30,
          chunks: 'all',
          minChunks: 1,
          reuseExistingChunk: true,
          enforce: true
        },
        
        // 公共组件分离
        commons: {
          name: 'chunk-commons',
          test: /[\\/]src[\\/]components[\\/]/,
          minChunks: 3,
          priority: 5,
          reuseExistingChunk: true
        }
      }
    })
    
    // 运行时分离
    config.optimization.runtimeChunk('single')
    
    // 生产环境优化
    if (isProduction) {
      // 删除 console.log
      config.optimization.minimizer('terser').tap(args => {
        args[0].terserOptions.compress.drop_console = true
        args[0].terserOptions.compress.drop_debugger = true
        args[0].terserOptions.compress.pure_funcs = ['console.log']
        return args
      })
      
      // 长期缓存优化
      config.output.filename('[name].[contenthash:8].js')
      config.output.chunkFilename('[name].[contenthash:8].js')
    }
  }
}
