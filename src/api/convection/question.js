import request from '@/utils/request'

// 强对流试题管理API
const ConvectionQuestionAPI = {
  /**
   * 获取试题列表
   * @param {Object} params 查询参数
   */
  getQuestionList(params) {
    return request({
      url: '/convection/question/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取题目详情
   * @param {String} questionId 题目ID
   */
  getQuestionDetail(questionId) {
    return request({
      url: `/convection/question/${questionId}`,
      method: 'get'
    })
  },

  /**
   * 创建试题
   * @param {Object} data 试题数据
   */
  createQuestion(data) {
    return request({
      url: '/convection/question',
      method: 'post',
      data
    })
  },

  /**
   * 更新试题
   * @param {String} questionId 题目ID
   * @param {Object} data 更新数据
   */
  updateQuestion(questionId, data) {
    return request({
      url: `/convection/question/${questionId}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除试题
   * @param {String} questionId 题目ID
   */
  deleteQuestion(questionId) {
    return request({
      url: `/convection/question/${questionId}`,
      method: 'delete'
    })
  },

  /**
   * 上传MICAPS文件
   * @param {FormData} formData 文件数据
   */
  uploadMicapsFile(formData) {
    return request({
      url: '/convection/question/upload/micaps',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传强对流落区文件
   * @param {FormData} formData 文件数据
   */
  uploadConvectionAreaFile(formData) {
    return request({
      url: '/convection/question/upload/area',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取标准答案
   * @param {String} questionId 题目ID
   */
  getStandardAnswer(questionId) {
    return request({
      url: `/convection/question/${questionId}/standard-answer`,
      method: 'get'
    })
  },

  /**
   * 设置标准答案
   * @param {String} questionId 题目ID
   * @param {Object} data 标准答案数据
   */
  setStandardAnswer(questionId, data) {
    return request({
      url: `/convection/question/${questionId}/standard-answer`,
      method: 'post',
      data
    })
  }
}

export default ConvectionQuestionAPI
