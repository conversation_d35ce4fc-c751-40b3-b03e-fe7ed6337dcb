<template>
  <div class="convection-question-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>强对流试题管理</h2>
        <div class="header-desc">管理强对流天气临近预报考试试题</div>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          icon="el-icon-plus"
          @click="handleCreate"
        >
          新建试题
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="试题标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入试题标题"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        
        <el-form-item label="试题难度">
          <el-select
            v-model="searchForm.difficulty"
            placeholder="请选择难度"
            clearable
            style="width: 120px;"
          >
            <el-option label="简单" value="easy" />
            <el-option label="中等" value="medium" />
            <el-option label="困难" value="hard" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search">
            搜索
          </el-button>
          <el-button @click="handleReset" icon="el-icon-refresh">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 试题列表 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="title" label="试题标题" min-width="200">
          <template slot-scope="scope">
            <div class="question-title">
              <div class="title-text">{{ scope.row.title }}</div>
              <div class="title-meta">
                <el-tag size="mini" type="info">ID: {{ scope.row.id }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="试题描述" min-width="250">
          <template slot-scope="scope">
            <div class="question-desc">
              {{ scope.row.description || '暂无描述' }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="MICAPS文件" width="120" align="center">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.micapsFiles && scope.row.micapsFiles.length > 0 ? 'success' : 'info'"
              size="mini"
            >
              {{ scope.row.micapsFiles ? scope.row.micapsFiles.length : 0 }}个文件
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="站点配置" width="120" align="center">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.stations && scope.row.stations.length > 0 ? 'success' : 'warning'"
              size="mini"
            >
              {{ scope.row.stations ? scope.row.stations.length : 0 }}个站点
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="试题难度" width="100" align="center">
          <template slot-scope="scope">
            <el-tag 
              :type="getDifficultyTagType(scope.row.difficulty)"
              size="small"
            >
              {{ getDifficultyLabel(scope.row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleView(scope.row)"
              icon="el-icon-view"
            >
              查看
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              icon="el-icon-edit"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleCopy(scope.row)"
              icon="el-icon-copy-document"
            >
              复制
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              style="color: #F56C6C;"
              icon="el-icon-delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRows.length > 0">
        <div class="batch-info">
          <span>已选择 {{ selectedRows.length }} 项</span>
        </div>
        <div class="batch-buttons">
          <el-button size="small" @click="handleBatchEnable">
            批量启用
          </el-button>
          <el-button size="small" @click="handleBatchDisable">
            批量禁用
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            批量删除
          </el-button>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>

    <!-- 试题详情/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="1200px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <div class="question-form">
        <el-form
          ref="questionForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          v-loading="formLoading"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>
            
            <el-form-item label="试题标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入试题标题"
                :readonly="isViewMode"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="试题描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="4"
                placeholder="请输入试题描述，包括背景信息、预报要求等"
                :readonly="isViewMode"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="试题难度" prop="difficulty">
                  <el-select 
                    v-model="formData.difficulty"
                    :disabled="isViewMode"
                    style="width: 100%;"
                    placeholder="请选择试题难度"
                  >
                    <el-option label="简单" value="easy" />
                    <el-option label="中等" value="medium" />
                    <el-option label="困难" value="hard" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <!-- MICAPS文件管理 -->
          <div class="form-section">
            <h4 class="section-title">MICAPS气象文件</h4>
            
            <div class="file-upload-area" v-if="!isViewMode">
              <el-upload
                ref="micapsUpload"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="uploadData"
                :file-list="micapsFileList"
                :on-success="handleMicapsUploadSuccess"
                :on-error="handleMicapsUploadError"
                :on-remove="handleMicapsFileRemove"
                :before-upload="beforeMicapsUpload"
                multiple
                drag
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将MICAPS文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  支持.000、.024等MICAPS格式文件，单个文件不超过50MB
                </div>
              </el-upload>
            </div>
            
            <div class="file-list" v-if="micapsFileList.length > 0">
              <h5>已上传文件：</h5>
              <div class="file-items">
                <div 
                  v-for="file in micapsFileList"
                  :key="file.uid || file.id"
                  class="file-item"
                >
                  <div class="file-info">
                    <i class="el-icon-document"></i>
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  </div>
                  <div class="file-actions" v-if="!isViewMode">
                    <el-button
                      type="text"
                      size="mini"
                      @click="downloadFile(file)"
                      icon="el-icon-download"
                    >
                      下载
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      @click="removeFile(file)"
                      style="color: #F56C6C;"
                      icon="el-icon-delete"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 站点配置 -->
          <div class="form-section">
            <h4 class="section-title">预报站点配置</h4>
            
            <div class="station-config">
              <div class="config-header">
                <span>预报站点列表（共需4个站点）</span>
                <el-button
                  v-if="!isViewMode"
                  type="primary"
                  size="mini"
                  @click="handleAddStation"
                  icon="el-icon-plus"
                  :disabled="formData.stations.length >= 4"
                >
                  添加站点
                </el-button>
              </div>
              
              <div class="station-list">
                <!-- 站点配置表格 -->
                <el-table
                  :data="formData.stations"
                  border
                  style="width: 100%"
                  v-if="formData.stations.length > 0"
                >
                  <el-table-column prop="name" label="站点名称" width="150">
                    <template slot-scope="scope">
                      <el-form-item :prop="`stations.${scope.$index}.name`">
                        <el-input
                          v-model="scope.row.name"
                          placeholder="请输入站点名称"
                          :readonly="isViewMode"
                          size="small"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="短时强降水" width="180">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.rainfallLevel"
                        placeholder="请选择"
                        :disabled="isViewMode"
                        clearable
                        size="small"
                        style="width: 100%;"
                      >
                        <el-option label="无" value="none" />
                        <el-option label="20≤R1＜40mm/h" value="level1" />
                        <el-option label="40≤R1＜80mm/h" value="level2" />
                        <el-option label="80≤R1mm/h以上" value="level3" />
                      </el-select>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="雷暴大风" width="200">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.windLevel"
                        placeholder="请选择"
                        :disabled="isViewMode"
                        clearable
                        size="small"
                        style="width: 100%;"
                      >
                        <el-option label="无" value="none" />
                        <el-option label="8级≤Wg＜10级或6级≤W2＜8级" value="moderate" />
                        <el-option label="10级≤Wg＜12级或8级≤W2＜10级" value="severe" />
                        <el-option label="12级≤Wg或龙卷或10级≤W2" value="extreme" />
                      </el-select>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="冰雹" width="150">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.hailLevel"
                        placeholder="请选择"
                        :disabled="isViewMode"
                        clearable
                        size="small"
                        style="width: 100%;"
                      >
                        <el-option label="无" value="none" />
                        <el-option label="2cm以上大冰雹" value="large" />
                      </el-select>
                    </template>
                  </el-table-column>
                  
                  <el-table-column label="操作" width="80" v-if="!isViewMode">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="mini"
                        @click="removeStation(scope.$index)"
                        style="color: #F56C6C;"
                        icon="el-icon-delete"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                
                <div v-if="formData.stations.length === 0" class="empty-stations">
                  <el-empty 
                    description="暂无配置站点，请添加4个预报站点"
                    :image-size="80"
                  />
                </div>
                
                <div v-if="formData.stations.length > 0 && formData.stations.length < 4" class="station-count-tip">
                  <el-alert
                    :title="`当前已配置 ${formData.stations.length} 个站点，还需添加 ${4 - formData.stations.length} 个站点`"
                    type="warning"
                    :closable="false"
                    show-icon
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- 强对流落区文件上传 -->
          <div class="form-section">
            <h4 class="section-title">强对流落区标准答案文件</h4>
            <div class="section-desc">
              <el-alert
                title="注意：强对流落区文件仅管理员可见，考生无法查看，用于自动评分对比"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
            
            <div class="area-file-upload" v-if="!isViewMode">
              <el-upload
                ref="areaFileUpload"
                :action="areaFileUploadUrl"
                :headers="uploadHeaders"
                :data="uploadData"
                :file-list="areaFileList"
                :on-success="handleAreaFileUploadSuccess"
                :on-error="handleAreaFileUploadError"
                :on-remove="handleAreaFileRemove"
                :before-upload="beforeAreaFileUpload"
                multiple
                drag
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将强对流落区文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  支持.json、.geojson、.shp等地理数据格式文件，单个文件不超过10MB
                </div>
              </el-upload>
            </div>
            
            <div class="area-file-list" v-if="areaFileList.length > 0">
              <h5>已上传落区文件：</h5>
              <div class="file-items">
                <div 
                  v-for="file in areaFileList"
                  :key="file.uid || file.id"
                  class="file-item"
                >
                  <div class="file-info">
                    <i class="el-icon-map-location"></i>
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                    <el-tag v-if="file.areaType" :type="getAreaTypeTagType(file.areaType)" size="mini">
                      {{ getAreaTypeLabel(file.areaType) }}
                    </el-tag>
                  </div>
                  <div class="file-actions" v-if="!isViewMode">
                    <el-button
                      type="text"
                      size="mini"
                      @click="previewAreaFile(file)"
                      icon="el-icon-view"
                    >
                      预览
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      @click="downloadFile(file)"
                      icon="el-icon-download"
                    >
                      下载
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      @click="removeAreaFile(file)"
                      style="color: #F56C6C;"
                      icon="el-icon-delete"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 标准预报依据录入 -->
          <div class="form-section">
            <h4 class="section-title">标准预报依据</h4>
            <div class="section-desc">
              <el-alert
                title="请详细录入标准预报依据，用于人工批卷时与考生答案对比"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
            
            <el-form-item label="标准预报依据" prop="standardReasoning">
              <el-input
                v-model="formData.standardReasoning"
                type="textarea"
                :rows="8"
                placeholder="请详细录入标准预报依据，包括：&#10;1. 分级依据阐述：各类强对流天气的分级判断标准&#10;2. 极端天气预报理由：极端天气形成机制和预报要点&#10;3. 气象分析：基于MICAPS资料的专业分析&#10;4. 预报逻辑：预报思路和判断过程"
                :readonly="isViewMode"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
            
            <div class="reasoning-tips">
              <h6>录入要点提示：</h6>
              <ul>
                <li><strong>分级依据阐述（10分）：</strong>详细说明短时强降水、雷暴大风、冰雹的分级判断标准</li>
                <li><strong>极端天气预报理由（10分）：</strong>阐述极端天气形成机制、预报关键指标和预警发布依据</li>
                <li><strong>气象分析：</strong>基于提供的MICAPS资料进行专业分析</li>
                <li><strong>预报逻辑：</strong>说明预报思路和判断过程</li>
              </ul>
                         </div>
           </div>
           

        </el-form>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button 
          v-if="!isViewMode"
          type="primary" 
          @click="handleSave"
          :loading="saving"
        >
          {{ isEditMode ? '更新' : '保存' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { convectionApi } from '@/api/convection/convection'

export default {
  name: 'ConvectionQuestionManage',
  
  data() {
    return {
      // 搜索表单
      searchForm: {
        title: '',
        difficulty: ''
      },
      
      // 表格数据
      tableData: [],
      selectedRows: [],
      loading: false,
      updating: false,
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      
      // 对话框
      dialogVisible: false,
      dialogMode: 'create', // create, edit, view
      formLoading: false,
      saving: false,
      
      // 表单数据
      formData: {
        id: null,
        title: '',
        description: '',
        difficulty: 'medium',
        micapsFiles: [],
        areaFiles: [],
        stations: [],
        forecastRegion: {
          centerLon: 116.4,
          centerLat: 39.9,
          zoom: 8
        },
        standardReasoning: ''
      },
      
      // 文件上传
      micapsFileList: [],
      uploadUrl: '/api/convection/question/upload/micaps',
      uploadHeaders: {
        'Authorization': `Bearer ${this.$store.getters.token}`
      },
      uploadData: {},
      
      // 强对流落区文件上传
      areaFileList: [],
      areaFileUploadUrl: '/api/convection/question/upload/area-file',
      areaUploadHeaders: {
        'Authorization': `Bearer ${this.$store.getters.token}`
      },
      areaUploadData: {},
      
      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入试题标题', trigger: 'blur' },
          { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入试题描述', trigger: 'blur' },
          { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        difficulty: [
          { required: true, message: '请选择试题难度', trigger: 'change' }
        ],
        standardReasoning: [
          { required: true, message: '请输入标准预报依据', trigger: 'blur' },
          { min: 50, max: 2000, message: '标准预报依据长度在 50 到 2000 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    dialogTitle() {
      const titleMap = {
        create: '新建试题',
        edit: '编辑试题',
        view: '查看试题'
      }
      return titleMap[this.dialogMode] || '试题管理'
    },
    
    isViewMode() {
      return this.dialogMode === 'view'
    },
    
    isEditMode() {
      return this.dialogMode === 'edit'
    },
    
    isCreateMode() {
      return this.dialogMode === 'create'
    }
  },
  
  created() {
    this.loadTableData()
  },
  
  methods: {
    // 获取难度标签类型
    getDifficultyTagType(difficulty) {
      const typeMap = {
        'easy': 'success',
        'medium': 'warning', 
        'hard': 'danger'
      }
      return typeMap[difficulty] || 'info'
    },
    
    // 获取难度标签文本
    getDifficultyLabel(difficulty) {
      const labelMap = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      }
      return labelMap[difficulty] || '未知'
    },
    
    // 获取天气类型标签类型
    getWeatherTypeTagType(type) {
      const typeMap = {
        'heavy-rainfall': 'primary',
        'thunderstorm-wind': 'warning',
        'hail': 'danger',
        'tornado': 'info'
      }
      return typeMap[type] || 'default'
    },
    
    // 获取天气类型标签文本
    getWeatherTypeLabel(type) {
      const labelMap = {
        'heavy-rainfall': '短时强降水',
        'thunderstorm-wind': '雷暴大风',
        'hail': '冰雹',
        'tornado': '龙卷'
      }
      return labelMap[type] || '未知类型'
    },

    // 获取落区文件类型标签类型
    getAreaTypeTagType(type) {
      const typeMap = {
        'json': 'info',
        'geojson': 'success',
        'shp': 'warning'
      }
      return typeMap[type] || 'default'
    },

    // 获取落区文件类型标签文本
    getAreaTypeLabel(type) {
      const labelMap = {
        'json': 'JSON',
        'geojson': 'GeoJSON',
        'shp': 'Shapefile'
      }
      return labelMap[type] || '未知类型'
    },

    // 获取短时强降水等级标签文本
    getRainfallLevelLabel(level) {
      const labelMap = {
        'level1': '20≤R1＜40mm/h',
        'level2': '40≤R1＜80mm/h',
        'level3': '80≤R1mm/h以上'
      }
      return labelMap[level] || '未知'
    },

    // 获取雷暴大风等级标签文本
    getWindLevelLabel(level) {
      const labelMap = {
        'moderate': '8级≤Wg＜10级或6级≤W2＜8级',
        'severe': '10级≤Wg＜12级或8级≤W2＜10级',
        'extreme': '12级≤Wg或龙卷或10级≤W2'
      }
      return labelMap[level] || '未知'
    },

    // 获取冰雹等级标签文本
    getHailLevelLabel(level) {
      const labelMap = {
        'large': '2cm以上大冰雹'
      }
      return labelMap[level] || '未知'
    },

    
    
    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          size: this.pagination.pageSize,
          title: this.searchForm.title,
          difficulty: this.searchForm.difficulty
        }
        
        const response = await convectionApi.getQuestionList(params)
        
        this.tableData = response.data.records || response.data.list || []
        this.pagination.total = response.data.total || 0
        
      } catch (error) {
        console.error('加载试题列表失败:', error)
        this.$message.error('加载试题列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadTableData()
    },
    
    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        difficulty: ''
      }
      this.pagination.currentPage = 1
      this.loadTableData()
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadTableData()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadTableData()
    },
    
    // 选择变化
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },
    

    
    // 新建试题
    handleCreate() {
      this.dialogMode = 'create'
      this.resetFormData()
      this.dialogVisible = true
    },
    
    // 查看试题
    async handleView(row) {
      this.dialogMode = 'view'
      await this.loadQuestionDetail(row.id)
      this.dialogVisible = true
    },
    
    // 编辑试题
    async handleEdit(row) {
      this.dialogMode = 'edit'
      await this.loadQuestionDetail(row.id)
      this.dialogVisible = true
    },
    
    // 复制试题
    async handleCopy(row) {
      this.$confirm('确定要复制这个试题吗？', '确认复制', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        try {
          await this.loadQuestionDetail(row.id)
          
          // 重置ID和标题
          this.formData.id = null
          this.formData.title = `${this.formData.title} - 副本`
          
          this.dialogMode = 'create'
          this.dialogVisible = true
          
        } catch (error) {
          this.$message.error('复制试题失败')
          console.error(error)
        }
      })
    },
    
    // 删除试题
    handleDelete(row) {
      this.$confirm('确定要删除这个试题吗？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await convectionApi.deleteQuestion(row.id)
          this.$message.success('试题删除成功')
          this.loadTableData()
          
        } catch (error) {
          this.$message.error('试题删除失败')
          console.error(error)
        }
      })
    },
    
    // 批量操作
    handleBatchEnable() {
      this.batchUpdateStatus(1, '启用')
    },
    
    handleBatchDisable() {
      this.batchUpdateStatus(0, '禁用')
    },
    
    async batchUpdateStatus(status, action) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要操作的试题')
        return
      }
      
      this.$confirm(`确定要批量${action}选中的试题吗？`, `确认批量${action}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        try {
          const promises = this.selectedRows.map(row => 
            convectionApi.updateQuestion(row.id, { status })
          )
          
          await Promise.all(promises)
          this.$message.success(`批量${action}成功`)
          this.loadTableData()
          
        } catch (error) {
          this.$message.error(`批量${action}失败`)
          console.error(error)
        }
      })
    },
    
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的试题')
        return
      }
      
      this.$confirm('确定要批量删除选中的试题吗？删除后无法恢复。', '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const promises = this.selectedRows.map(row => 
            convectionApi.deleteQuestion(row.id)
          )
          
          await Promise.all(promises)
          this.$message.success('批量删除成功')
          this.loadTableData()
          
        } catch (error) {
          this.$message.error('批量删除失败')
          console.error(error)
        }
      })
    },
    
    // 加载试题详情
    async loadQuestionDetail(questionId) {
      this.formLoading = true
      try {
        const response = await convectionApi.getQuestionDetail(questionId)
        const data = response.data
        
        this.formData = {
          id: data.id,
          title: data.title || '',
          description: data.description || '',
          difficulty: data.difficulty || 'medium',
          micapsFiles: data.micapsFiles || [],
          areaFiles: data.areaFiles || [],
          stations: data.stations || [],
          forecastRegion: data.forecastRegion || {
            centerLon: 116.4,
            centerLat: 39.9,
            zoom: 8
          },
          standardReasoning: data.standardReasoning || ''
        }
        
        // 设置文件列表
        this.micapsFileList = (data.micapsFiles || []).map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          url: file.url,
          uid: file.id
        }))

        // 设置落区文件列表
        this.areaFileList = (data.areaFiles || []).map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          url: file.url,
          uid: file.id,
          areaType: file.areaType
        }))
        
      } catch (error) {
        this.$message.error('加载试题详情失败')
        console.error(error)
      } finally {
        this.formLoading = false
      }
    },
    
    // 重置表单数据
    resetFormData() {
      this.formData = {
        id: null,
        title: '',
        description: '',
        difficulty: 'medium',
        micapsFiles: [],
        areaFiles: [],
        stations: [],
        forecastRegion: {
          centerLon: 116.4,
          centerLat: 39.9,
          zoom: 8
        },
        standardReasoning: ''
      }
      this.micapsFileList = []
      this.areaFileList = []
      
      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.questionForm) {
          this.$refs.questionForm.clearValidate()
        }
      })
    },
    
    // 添加站点
    handleAddStation() {
      this.formData.stations.push({
        id: null,
        name: '',
        rainfallLevel: null,
        windLevel: null,
        hailLevel: null
      })
    },
    
    // 删除站点
    removeStation(index) {
      this.formData.stations.splice(index, 1)
    },
    
    // 文件上传相关
    beforeMicapsUpload(file) {
      const isValidType = /\.(000|024|dat|txt)$/i.test(file.name)
      const isLt50M = file.size / 1024 / 1024 < 50
      
      if (!isValidType) {
        this.$message.error('只支持 .000、.024、.dat、.txt 格式的文件')
        return false
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB')
        return false
      }
      return true
    },
    
    handleMicapsUploadSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.micapsFileList = fileList
        this.formData.micapsFiles = fileList.map(f => ({
          id: f.response?.data?.id || f.id,
          name: f.name,
          size: f.size,
          url: f.response?.data?.url || f.url
        }))
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },
    
    handleMicapsUploadError(error, file, fileList) {
      this.$message.error('文件上传失败')
      console.error(error)
    },
    
    handleMicapsFileRemove(file, fileList) {
      this.micapsFileList = fileList
      this.formData.micapsFiles = fileList.map(f => ({
        id: f.response?.data?.id || f.id,
        name: f.name,
        size: f.size,
        url: f.response?.data?.url || f.url
      }))
    },

    // 强对流落区文件上传相关
    beforeAreaFileUpload(file) {
      const isValidType = /\.(json|geojson|shp)$/i.test(file.name)
      const isLt10M = file.size / 1024 / 1024 < 10
      
      if (!isValidType) {
        this.$message.error('只支持 .json、.geojson、.shp 格式的文件')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB')
        return false
      }
      return true
    },

    handleAreaFileUploadSuccess(response, file, fileList) {
      if (response.code === 0) {
        this.areaFileList = fileList
        this.$message.success('文件上传成功')
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    handleAreaFileUploadError(error, file, fileList) {
      this.$message.error('文件上传失败')
      console.error(error)
    },

    handleAreaFileRemove(file, fileList) {
      this.areaFileList = fileList
    },

    // 预览落区文件
    previewAreaFile(file) {
      if (file.url) {
        window.open(file.url, '_blank')
      } else {
        this.$message.warning('文件预览链接不存在')
      }
    },

    // 下载文件
    downloadFile(file) {
      if (file.url) {
        window.open(file.url, '_blank')
      } else {
        this.$message.warning('文件下载链接不存在')
      }
    },
    
    // 删除文件
    removeFile(file) {
      const index = this.micapsFileList.findIndex(f => 
        (f.uid || f.id) === (file.uid || file.id)
      )
      if (index !== -1) {
        this.micapsFileList.splice(index, 1)
        this.formData.micapsFiles.splice(index, 1)
      }
    },

    // 删除落区文件
    removeAreaFile(file) {
      const index = this.areaFileList.findIndex(f => 
        (f.uid || f.id) === (file.uid || file.id)
      )
      if (index !== -1) {
        this.areaFileList.splice(index, 1)
      }
    },
    
    // 保存试题
    async handleSave() {
      try {
        await this.$refs.questionForm.validate()
        
        this.saving = true
        
        const data = {
          ...this.formData,
          micapsFiles: this.formData.micapsFiles,
          areaFiles: this.areaFileList.map(f => ({
            id: f.response?.data?.id || f.id,
            name: f.name,
            size: f.size,
            url: f.response?.data?.url || f.url,
            areaType: f.areaType || this.getFileTypeFromName(f.name)
          })),
          stations: this.formData.stations.filter(station => 
            station.name && station.name.trim() !== ''
          )
        }
        
        if (this.isEditMode) {
          await convectionApi.updateQuestion(data.id, data)
          this.$message.success('试题更新成功')
        } else {
          await convectionApi.createQuestion(data)
          this.$message.success('试题创建成功')
        }
        
        this.dialogVisible = false
        this.loadTableData()
        
      } catch (error) {
        if (error !== false) { // 排除表单验证失败
          this.$message.error('保存试题失败')
          console.error(error)
        }
      } finally {
        this.saving = false
      }
    },
    
    // 关闭对话框
    handleDialogClose() {
      if (this.saving) {
        return
      }
      
      this.dialogVisible = false
      this.resetFormData()
    },
    
    // 工具方法
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 根据文件名获取文件类型
    getFileTypeFromName(fileName) {
      if (!fileName) return 'unknown'
      const ext = fileName.split('.').pop().toLowerCase()
      const typeMap = {
        'json': 'json',
        'geojson': 'geojson', 
        'shp': 'shp'
      }
      return typeMap[ext] || 'unknown'
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-question-manage {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: bold;
      }
      
      .header-desc {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .question-title {
      .title-text {
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .title-meta {
        font-size: 12px;
      }
    }
    
    .question-desc {
      color: #606266;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background: #F8F9FA;
      border-top: 1px solid #E4E7ED;
      
      .batch-info {
        color: #606266;
        font-size: 14px;
      }
      
      .batch-buttons {
        display: flex;
        gap: 10px;
      }
    }
    
    .pagination-section {
      padding: 20px;
      text-align: center;
      border-top: 1px solid #E4E7ED;
    }
  }
  
  .question-form {
    .form-section {
      margin-bottom: 30px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-title {
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        color: #303133;
        font-size: 16px;
        font-weight: bold;
        border-bottom: 2px solid #E1F0FF;
      }
      
      .section-desc {
        margin-bottom: 20px;
      }
    }
    
    .file-upload-area {
      margin-bottom: 20px;
      
      ::v-deep .el-upload-dragger {
        width: 100%;
        height: 120px;
      }
    }
    
    .file-list {
      h5 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 14px;
        font-weight: bold;
      }
      
      .file-items {
        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 15px;
          background: #F8F9FA;
          border: 1px solid #E9ECEF;
          border-radius: 6px;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
            
            i {
              color: #409EFF;
              font-size: 16px;
            }
            
            .file-name {
              color: #303133;
              font-weight: bold;
            }
            
            .file-size {
              color: #909399;
              font-size: 12px;
            }
          }
          
          .file-actions {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
    
    .area-file-upload {
      margin-bottom: 20px;
      
      ::v-deep .el-upload-dragger {
        width: 100%;
        height: 120px;
      }
    }
    
    .area-file-list {
      h5 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 14px;
        font-weight: bold;
      }
      
      .file-items {
        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 15px;
          background: #F8F9FA;
          border: 1px solid #E9ECEF;
          border-radius: 6px;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
            
            i {
              color: #67C23A;
              font-size: 16px;
            }
            
            .file-name {
              color: #303133;
              font-weight: bold;
            }
            
            .file-size {
              color: #909399;
              font-size: 12px;
            }
          }
          
          .file-actions {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
    
    .station-config {
      .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        span {
          color: #303133;
          font-weight: bold;
        }
      }
      
      .station-list {
        .empty-stations {
          text-align: center;
          padding: 40px 20px;
          background: #F8F9FA;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .station-count-tip {
          margin-top: 15px;
        }
        
        // 表格内表单项样式优化
        ::v-deep .el-table {
          .el-form-item {
            margin-bottom: 0;
            
            .el-form-item__content {
              margin-left: 0 !important;
            }
          }
          
          .el-table__body-wrapper {
            .el-table__row {
              &:hover {
                background-color: #F5F7FA;
              }
            }
          }
        }
      }
    }

         

    .reasoning-tips {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px dashed #E9ECEF;

      h6 {
        margin-bottom: 10px;
        color: #303133;
        font-size: 14px;
        font-weight: bold;
      }

      ul {
        padding-left: 20px;
        list-style: disc;
      }

      li {
        color: #606266;
        font-size: 13px;
        line-height: 1.6;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .convection-question-manage {
    .page-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
    }
    
    .search-section .search-form {
      .el-form-item {
        margin-bottom: 15px;
        
        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }
    
    .question-form .station-config .station-list .station-item {
      flex-direction: column;
      
      .station-actions {
        padding-top: 0;
        width: 100%;
        text-align: center;
      }
    }
  }
  
  // 天气类型显示样式
  .weather-types-display {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #409eff;
    
    .types-label {
      font-size: 12px;
      color: #606266;
      margin-right: 8px;
    }
  }
}
</style>
