<template>
  <div class="weather-qu-form">
    <el-card class="form-card">
      <div slot="header" class="card-header">
        <span>{{ isEdit ? '编辑' : '新增' }}天气预报表格题</span>
      </div>

      <el-form
        ref="weatherQuForm"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="weather-form"
      >
        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="formData.content"
            :rows="4"
            type="textarea"
            placeholder="请输入题目内容，例如：根据以下气象资料，请预报2035年9月9日08时未来24小时内指定站点的灾害性天气和气象要素"
          />
        </el-form-item>

        <el-form-item label="题目图片" prop="image">
          <file-upload
            v-model="formData.image"
            :limit="1"
            accept="image/*"
            list-type="picture-card"
          />
        </el-form-item>

        <el-form-item label="表格配置" prop="weatherConfigId">
          <el-select
            v-model="formData.weatherConfigId"
            placeholder="请选择表格配置"
            style="width: 100%"
            @change="onConfigChange"
          >
            <el-option
              v-for="config in configOptions"
              :key="config.id"
              :label="config.name"
              :value="config.id"
            >
              <span style="float: left">{{ config.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ config.templateType }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedConfig" label="配置预览">
          <el-card class="config-preview">
            <div class="preview-header">
              <h4>{{ selectedConfig.title }}</h4>
              <p>{{ selectedConfig.description }}</p>
            </div>
            <div class="preview-table">
              <el-table
                :data="previewData"
                border
                size="small"
                max-height="300"
              >
                <el-table-column
                  v-for="column in tableColumns"
                  :key="column.field"
                  :prop="column.field"
                  :label="column.title"
                  :width="column.width"
                  align="center"
                />
              </el-table>
            </div>
          </el-card>
        </el-form-item>

        <el-form-item label="难度等级" prop="level">
          <el-radio-group v-model="formData.level">
            <el-radio :label="1">普通</el-radio>
            <el-radio :label="2">较难</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="分值" prop="score">
          <el-input-number
            v-model="formData.score"
            :min="1"
            :max="100"
            placeholder="请输入题目分值"
          />
        </el-form-item>

        <el-form-item label="归属题库" prop="repoIds">
          <repo-select v-model="formData.repoIds" multiple />
        </el-form-item>

        <el-form-item label="题目备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入题目备注（可选）"
          />
        </el-form-item>

        <el-form-item label="整题解析" prop="analysis">
          <el-input
            v-model="formData.analysis"
            :rows="4"
            type="textarea"
            placeholder="请输入整题解析（可选）"
          />
        </el-form-item>

        <el-form-item>
          <el-button :loading="submitting" type="primary" @click="submitForm">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getActiveConfigs } from '@/api/weather/weather'
import { save, detail } from '@/api/qu/qu'
import FileUpload from '@/components/FileUpload'
import RepoSelect from '@/components/RepoSelect'

export default {
  name: 'WeatherQuForm',
  components: {
    FileUpload,
    RepoSelect
  },
  data() {
    return {
      isEdit: false,
      submitting: false,
      configOptions: [],
      selectedConfig: null,
      formData: {
        id: '',
        content: '',
        image: '',
        weatherConfigId: '',
        level: 1,
        score: 10,
        repoIds: [],
        remark: '',
        analysis: '',
        quType: 6 // 天气预报表格题
      },
      formRules: {
        content: [
          { required: true, message: '题目内容不能为空', trigger: 'blur' }
        ],
        weatherConfigId: [
          { required: true, message: '请选择表格配置', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择难度等级', trigger: 'change' }
        ],
        score: [
          { required: true, message: '请输入题目分值', trigger: 'blur' }
        ],
        repoIds: [
          { required: true, message: '请选择归属题库', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    tableColumns() {
      if (!this.selectedConfig || !this.selectedConfig.tableSchema) {
        return []
      }
      return this.selectedConfig.tableSchema.columns || []
    },
    previewData() {
      if (!this.selectedConfig || !this.selectedConfig.tableSchema) {
        return []
      }

      const rows = this.selectedConfig.tableSchema.rows || 6
      const data = []

      for (let i = 0; i < Math.min(rows, 3); i++) {
        const row = {}
        this.tableColumns.forEach(column => {
          if (column.field === 'forecastTime') {
            row[column.field] = `2035年09月09日${8 + i * 4}时`
          } else if (column.field === 'stationInfo') {
            row[column.field] = '站点'
          } else {
            row[column.field] = '...'
          }
        })
        data.push(row)
      }

      return data
    }
  },
  created() {
    this.loadConfigs()

    const id = this.$route.params.id
    if (id) {
      this.isEdit = true
      this.loadQuestionDetail(id)
    }
  },
  methods: {
    async loadConfigs() {
      try {
        const response = await getActiveConfigs()
        if (response.code === 200) {
          this.configOptions = response.data || []
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载表格配置失败')
      }
    },

    async loadQuestionDetail(id) {
      try {
        const response = await detail({ id })
        if (response.code === 200) {
          this.formData = { ...this.formData, ...response.data }
          if (this.formData.weatherConfigId) {
            this.onConfigChange(this.formData.weatherConfigId)
          }
        }
      } catch (error) {
        console.error('加载题目详情失败:', error)
        this.$message.error('加载题目详情失败')
      }
    },

    onConfigChange(configId) {
      this.selectedConfig = this.configOptions.find(config => config.id === configId)
    },

    submitForm() {
      this.$refs.weatherQuForm.validate(async(valid) => {
        if (!valid) {
          return false
        }

        this.submitting = true
        try {
          const response = await save(this.formData)
          if (response.code === 200) {
            this.$message.success(this.isEdit ? '更新成功' : '创建成功')
            this.goBack()
          } else {
            this.$message.error(response.message || '操作失败')
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error('保存失败')
        } finally {
          this.submitting = false
        }
      })
    },

    resetForm() {
      this.$refs.weatherQuForm.resetFields()
      this.selectedConfig = null
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.weather-qu-form {
  padding: 20px;

  .form-card {
    max-width: 800px;
    margin: 0 auto;

    .card-header {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .weather-form {
    .config-preview {
      margin-top: 10px;

      .preview-header {
        margin-bottom: 15px;
        text-align: center;

        h4 {
          margin: 0 0 8px 0;
          color: #303133;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
        }
      }

      .preview-table {
        ::v-deep .el-table {
          .el-table__header-wrapper {
            .el-table__header {
              th {
                background-color: #f5f7fa;
                color: #606266;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}
</style>
