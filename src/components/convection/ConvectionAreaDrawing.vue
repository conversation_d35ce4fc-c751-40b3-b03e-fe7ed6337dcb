<template>
  <div class="convection-area-drawing">
    <!-- 组件标题 -->
    <div class="drawing-header">
      <div class="title-section">
        <h3>强对流落区绘制</h3>
        <div class="subtitle">请在地图上绘制0-1小时强对流天气落区预报</div>
      </div>
      
      <div class="progress-section">
        <div class="progress-info">
          <span>绘制进度：{{ progress }}%</span>
          <el-progress :percentage="progress" :show-text="false" size="small" />
        </div>
        <div class="area-count">
          <span>已绘制区域：{{ totalAreaCount }}个</span>
        </div>
      </div>
    </div>

    <!-- 绘制工具栏 -->
    <div class="drawing-toolbar">
      <div class="tool-group">
        <div class="group-title">绘制工具</div>
        <div class="tool-buttons">
          <el-button
            v-for="tool in drawingTools"
            :key="tool.type"
            :type="currentTool === tool.type ? 'primary' : 'default'"
            :class="{ 'active-tool': currentTool === tool.type }"
            size="small"
            @click="setCurrentTool(tool.type)"
            :disabled="readonly"
          >
            <div class="tool-content">
              <div 
                class="tool-color" 
                :style="{ backgroundColor: tool.color }"
              ></div>
              <span class="tool-label">{{ tool.label }}</span>
              <span class="tool-count">({{ getAreaCount(tool.type) }})</span>
            </div>
          </el-button>
        </div>
      </div>
      
      <div class="action-group">
        <div class="group-title">操作</div>
        <div class="action-buttons">
          <el-button 
            size="small" 
            @click="startDrawing()"
            :disabled="readonly || !currentTool || isDrawing"
            type="success"
            :loading="isDrawing"
          >
            <i class="el-icon-edit"></i>
            {{ isDrawing ? '绘制中...' : '开始绘制' }}
          </el-button>
          
          <el-button 
            size="small" 
            @click="stopDrawing()"
            :disabled="readonly || !isDrawing"
            type="warning"
          >
            <i class="el-icon-close"></i>
            停止绘制
          </el-button>
          
          <el-button 
            size="small" 
            @click="clearCurrentType()"
            :disabled="readonly || !currentTool || getAreaCount(currentTool) === 0"
            type="danger"
            plain
          >
            <i class="el-icon-delete"></i>
            清除当前类型
          </el-button>
          
          <el-button 
            size="small" 
            @click="clearAllAreas()"
            :disabled="readonly || totalAreaCount === 0"
            type="danger"
          >
            <i class="el-icon-delete"></i>
            清空全部
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 地图容器 -->
    <div class="map-container">
      <div class="map-content">
        <div id="convection-map" class="map-canvas" :class="{ 'readonly': readonly }">
          <!-- 地图加载状态 -->
          <div v-if="!mapLoaded" class="map-loading">
            <el-loading-spinner></el-loading-spinner>
            <span>地图加载中...</span>
          </div>

          <!-- 地图图例 -->
          <div v-if="mapLoaded" class="map-legend">
            <div class="legend-header">
              <i class="el-icon-view"></i>
              <span class="legend-title">强对流天气类型图例</span>
            </div>
            <div class="legend-content">
              <div 
                v-for="tool in drawingTools"
                :key="tool.type"
                class="legend-item"
                :class="{ 'active': currentTool === tool.type }"
              >
                <div 
                  class="legend-color"
                  :style="{ backgroundColor: tool.color }"
                ></div>
                <div class="legend-info">
                  <div class="legend-label">{{ tool.label }}</div>
                  <div class="legend-desc">{{ tool.description }}</div>
                  <div class="legend-count">{{ getAreaCount(tool.type) }}个区域</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作提示 -->
          <div v-if="mapLoaded && !readonly" class="operation-tips">
            <div class="tip-item">
              <i class="el-icon-mouse"></i>
              <span>左键单击添加点，双击结束绘制</span>
            </div>
            <div class="tip-item">
              <i class="el-icon-edit-outline"></i>
              <span>绘制完成后可拖拽节点修改形状</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 绘制区域列表 -->
    <div class="area-list" v-if="totalAreaCount > 0">
      <div class="list-header">
        <h4>已绘制区域列表</h4>
        <el-button 
          type="text" 
          size="mini" 
          @click="toggleAreaList"
        >
          {{ areaListExpanded ? '收起' : '展开' }}
        </el-button>
      </div>
      
      <div class="list-content" v-show="areaListExpanded">
        <div 
          v-for="(areas, type) in convectionAreas"
          :key="type"
          class="area-type-group"
          v-if="areas.length > 0"
        >
          <div class="type-header">
            <div 
              class="type-color"
              :style="{ backgroundColor: getToolByType(type).color }"
            ></div>
            <span class="type-name">{{ getToolByType(type).label }}</span>
            <span class="type-count">({{ areas.length }}个)</span>
          </div>
          
          <div class="area-items">
            <div 
              v-for="(area, index) in areas"
              :key="area.id"
              class="area-item"
              @click="highlightArea(area.id)"
              @mouseenter="highlightArea(area.id)"
              @mouseleave="clearHighlight()"
            >
              <div class="area-info">
                <span class="area-name">区域 {{ index + 1 }}</span>
                <span class="area-time">{{ formatTime(area.timestamp || (area.properties && area.properties.createTime)) }}</span>
              </div>
              <div class="area-actions" v-if="!readonly">
                <el-button 
                  type="text" 
                  size="mini" 
                  @click.stop="deleteArea(area.id)"
                  title="删除"
                  style="color: #F56C6C;"
                >
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips-panel" v-if="!readonly">
      <el-alert
        :title="getCurrentToolTip()"
        type="info"
        :closable="false"
        show-icon
      >
        <div slot="description">
          <ul>
            <li>选择绘制工具后，点击"开始绘制"按钮</li>
            <li>在地图上点击绘制多边形区域，双击完成绘制</li>
            <li>可以绘制多个不连续的区域</li>
            <li>绘制完成后可以拖拽节点修改形状</li>
            <li>不同类型的强对流天气使用不同颜色标识</li>
          </ul>
        </div>
      </el-alert>
    </div>
  </div>
</template>

<script>
import 'ol/ol.css'
import { Map, View } from 'ol'
import LayerVector from 'ol/layer/Vector'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'
import SourceVector from 'ol/source/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { Style, Stroke, Fill } from 'ol/style'
import Draw from 'ol/interaction/Draw'
import Modify from 'ol/interaction/Modify'
import Snap from 'ol/interaction/Snap'

// 天地图token
const tk = 'ef7e830b6e72f9990997257effbc61c7'

export default {
  name: 'ConvectionAreaDrawing',
  
  props: {
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 预报区域配置
    region: {
      type: Object,
      required: true,
      default: () => ({
        centerLon: 116.4,
        centerLat: 39.9,
        zoom: 8
      })
    },
    // 只读模式
    readonly: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      // 地图相关
      map: null,
      mapLoaded: false,
      baseLayer: null,
      annotationLayer: null,
      convectionSource: null,
      convectionLayer: null,
      drawInteraction: null,
      modifyInteraction: null,
      snapInteraction: null,
      
      // 绘制状态
      currentTool: null,
      isDrawing: false,
      progress: 0,
      
      // UI状态
      areaListExpanded: true,
      
      // 绘制工具配置
      drawingTools: [
        {
          type: 'heavy-rainfall',
          label: '强降水',
          description: '短时强降水落区',
          color: '#0066FF',
          icon: 'el-icon-cloudy-and-sunny'
        },
        {
          type: 'thunderstorm-wind',
          label: '雷暴大风',
          description: '雷暴大风影响区域',
          color: '#FF6600',
          icon: 'el-icon-lightning'
        },
        {
          type: 'hail',
          label: '冰雹',
          description: '冰雹落区',
          color: '#CC0000',
          icon: 'el-icon-heavy-rain'
        },
        {
          type: 'tornado',
          label: '龙卷',
          description: '龙卷风影响路径',
          color: '#660099',
          icon: 'el-icon-wind-power'
        }
      ],
      
      // 绘制数据
      convectionAreas: {
        'heavy-rainfall': [],
        'thunderstorm-wind': [],
        'hail': [],
        'tornado': []
      }
    }
  },
  
  computed: {
    // 总区域数量
    totalAreaCount() {
      return Object.values(this.convectionAreas)
        .reduce((sum, areas) => sum + areas.length, 0)
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      this.initMap()
      this.loadInitialData()
    })
  },
  
  beforeDestroy() {
    this.destroyMap()
  },
  
  methods: {
    // 初始化地图
    initMap() {
      try {
        const mapElement = document.getElementById('convection-map')
        if (!mapElement) {
          console.error('地图容器元素未找到')
          return
        }

        console.log('开始初始化强对流落区绘制地图...')

        // 创建天地图底图图层
        this.baseLayer = new TileLayer({
          source: new XYZ({
            url: `http://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tk}`,
            crossOrigin: 'anonymous'
          })
        })

        // 创建天地图注记图层
        this.annotationLayer = new TileLayer({
          source: new XYZ({
            url: `http://t{0-7}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tk}`,
            crossOrigin: 'anonymous'
          }),
          opacity: 0.8
        })

        // 创建强对流绘制图层
        this.convectionSource = new SourceVector({
          wrapX: false
        })
        this.convectionLayer = new LayerVector({
          source: this.convectionSource,
          style: this.getAreaStyle.bind(this),
          zIndex: 5
        })

        // 创建地图
        this.map = new Map({
          target: 'convection-map',
          layers: [
            this.baseLayer,
            this.annotationLayer,
            this.convectionLayer
          ],
          view: new View({
            center: fromLonLat([this.region.centerLon, this.region.centerLat]),
            zoom: this.region.zoom || 8,
            minZoom: 5,
            maxZoom: 15
          })
        })

        // 添加修改交互
        this.modifyInteraction = new Modify({
          source: this.convectionSource
        })
        if (!this.readonly) {
          this.map.addInteraction(this.modifyInteraction)
          
          // 监听修改事件
          this.modifyInteraction.on('modifyend', () => {
            this.updateAreaData()
          })
        }

        // 添加对齐交互
        this.snapInteraction = new Snap({
          source: this.convectionSource
        })
        if (!this.readonly) {
          this.map.addInteraction(this.snapInteraction)
        }

        this.mapLoaded = true
        console.log('强对流落区绘制地图初始化完成')
        
      } catch (error) {
        console.error('地图初始化失败:', error)
        this.$message.error('地图初始化失败，请刷新页面重试')
      }
    },
    
    // 设置当前绘制工具
    setCurrentTool(toolType) {
      if (this.readonly) return
      
      // 如果正在绘制，先停止
      if (this.isDrawing) {
        this.stopDrawing()
      }
      
      this.currentTool = this.currentTool === toolType ? null : toolType
      
      if (this.currentTool) {
        const tool = this.drawingTools.find(t => t.type === toolType)
        this.$message.info(`已选择${tool.label}绘制工具`)
      }
    },
    
    // 开始绘制
    startDrawing() {
      if (!this.currentTool || this.readonly || this.isDrawing) return

      this.isDrawing = true

      this.drawInteraction = new Draw({
        source: this.convectionSource,
        type: 'Polygon',
        style: this.getDrawingStyle()
      })

      this.drawInteraction.on('drawend', (event) => {
        const feature = event.feature
        const geometry = feature.getGeometry()

        // 设置要素属性
        const areaId = `area_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        feature.setProperties({
          convectionType: this.currentTool,
          areaId: areaId,
          timestamp: Date.now()
        })

        // 保存到数据结构
        const geoJson = new GeoJSON().writeGeometry(geometry)
        this.convectionAreas[this.currentTool].push({
          id: areaId,
          type: this.currentTool,
          geometry: geoJson,
          timestamp: Date.now()
        })

        this.updateAreaData()
        this.updateProgress()
        
        const tool = this.drawingTools.find(t => t.type === this.currentTool)
        this.$message.success(`已绘制${tool.label}区域`)
        
        // 不自动停止绘制，允许连续绘制
      })

      this.map.addInteraction(this.drawInteraction)
      
      const tool = this.drawingTools.find(t => t.type === this.currentTool)
      this.$message.info(`开始绘制${tool.label}区域，在地图上点击绘制多边形`)
    },
    
    // 停止绘制
    stopDrawing() {
      if (this.drawInteraction) {
        this.map.removeInteraction(this.drawInteraction)
        this.drawInteraction = null
      }
      this.isDrawing = false
    },
    
    // 清除当前类型的落区
    clearCurrentType() {
      if (!this.currentTool || this.readonly) return

      this.$confirm(`确定要清除所有${this.getToolByType(this.currentTool).label}区域吗？`, '确认清除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除地图上的要素
        const features = this.convectionSource.getFeatures()
        features.forEach(feature => {
          if (feature.get('convectionType') === this.currentTool) {
            this.convectionSource.removeFeature(feature)
          }
        })

        // 清除数据
        this.convectionAreas[this.currentTool] = []
        this.updateAreaData()
        this.updateProgress()
        
        const tool = this.getToolByType(this.currentTool)
        this.$message.success(`已清除所有${tool.label}区域`)
      })
    },
    
    // 清空所有区域
    clearAllAreas() {
      if (this.readonly || this.totalAreaCount === 0) return
      
      this.$confirm('确定要清空所有绘制的落区吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清空地图上的所有要素
        this.convectionSource.clear()
        
        // 清空所有区域数据
        Object.keys(this.convectionAreas).forEach(key => {
          this.convectionAreas[key] = []
        })
        
        this.updateAreaData()
        this.updateProgress()
        
        this.$message.success('已清空所有落区')
      })
    },
    
    // 删除指定区域
    deleteArea(areaId) {
      this.$confirm('确定要删除这个区域吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从地图上删除要素
        const features = this.convectionSource.getFeatures()
        features.forEach(feature => {
          if (feature.get('areaId') === areaId) {
            this.convectionSource.removeFeature(feature)
          }
        })
        
        // 从数据中删除
        Object.keys(this.convectionAreas).forEach(type => {
          const index = this.convectionAreas[type].findIndex(area => area.id === areaId)
          if (index !== -1) {
            this.convectionAreas[type].splice(index, 1)
          }
        })
        
        this.updateAreaData()
        this.updateProgress()
        this.$message.success('区域已删除')
      })
    },
    
    // 获取落区样式
    getAreaStyle(feature) {
      const convectionType = feature.get('convectionType')
      const tool = this.drawingTools.find(t => t.type === convectionType)

      if (!tool) return null

      return new Style({
        stroke: new Stroke({
          color: tool.color,
          width: 3
        }),
        fill: new Fill({
          color: tool.color + '40' // 添加透明度
        })
      })
    },
    
    // 获取绘制时的样式
    getDrawingStyle() {
      const tool = this.drawingTools.find(t => t.type === this.currentTool)

      return new Style({
        stroke: new Stroke({
          color: tool.color,
          width: 3,
          lineDash: [5, 5] // 虚线表示正在绘制
        }),
        fill: new Fill({
          color: tool.color + '20'
        })
      })
    },
    
    // 获取指定类型的区域数量
    getAreaCount(toolType) {
      return this.convectionAreas[toolType] ? this.convectionAreas[toolType].length : 0
    },
    
    // 根据类型获取工具配置
    getToolByType(type) {
      return this.drawingTools.find(tool => tool.type === type) || {}
    },
    
    // 更新绘制进度
    updateProgress() {
      // 简单的进度计算：每绘制一个区域增加25%进度，最多100%
      const progress = Math.min(this.totalAreaCount * 25, 100)
      this.progress = progress
      this.$emit('progress-change', progress)
    },
    
    // 更新区域数据并触发事件
    updateAreaData() {
      const data = {
        convectionAreas: { ...this.convectionAreas },
        totalAreaCount: this.totalAreaCount,
        lastModified: new Date().toISOString()
      }
      
      this.$emit('data-change', data)
    },
    
    // 加载初始数据
    loadInitialData() {
      if (!this.initialData.convectionAreas) return
      
      // 加载已有的区域数据
      Object.keys(this.initialData.convectionAreas).forEach(type => {
        if (this.convectionAreas[type]) {
          this.convectionAreas[type] = [...this.initialData.convectionAreas[type]]
        }
      })
      
      // 在地图上渲染已有区域
      this.renderExistingAreas()
      this.updateProgress()
    },
    
    // 渲染已有区域
    renderExistingAreas() {
      if (!this.mapLoaded) return
      
      const geoJsonFormat = new GeoJSON()
      
      Object.keys(this.convectionAreas).forEach(type => {
        this.convectionAreas[type].forEach(area => {
          try {
            const feature = geoJsonFormat.readFeature({
              type: 'Feature',
              geometry: area.geometry
            })
            
            feature.setProperties({
              convectionType: type,
              areaId: area.id,
              timestamp: area.timestamp
            })
            
            this.convectionSource.addFeature(feature)
          } catch (error) {
            console.error('渲染区域失败:', error, area)
          }
        })
      })
    },
    
    // 获取当前工具提示
    getCurrentToolTip() {
      if (!this.currentTool) {
        return '请选择绘制工具'
      }
      
      const tool = this.drawingTools.find(t => t.type === this.currentTool)
      return `当前工具：${tool.label} - ${tool.description}`
    },
    
    // 切换区域列表显示
    toggleAreaList() {
      this.areaListExpanded = !this.areaListExpanded
    },
    
    // 高亮区域 (暂时不实现，可以后续扩展)
    highlightArea(areaId) {
      // TODO: 实现区域高亮功能
    },
    
    // 清除高亮
    clearHighlight() {
      // TODO: 清除区域高亮
    },
    
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '-'
      const time = new Date(timestamp)
      return time.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    // 销毁地图
    destroyMap() {
      if (this.drawInteraction) {
        this.map.removeInteraction(this.drawInteraction)
      }
      if (this.modifyInteraction) {
        this.map.removeInteraction(this.modifyInteraction)
      }
      if (this.snapInteraction) {
        this.map.removeInteraction(this.snapInteraction)
      }
      if (this.map) {
        this.map.setTarget(null)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.convection-area-drawing {
  .drawing-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .title-section {
      flex: 1;
      
      h3 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 18px;
        font-weight: bold;
      }
      
      .subtitle {
        color: #909399;
        font-size: 13px;
        line-height: 1.4;
      }
    }
    
    .progress-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      
      .progress-info {
        display: flex;
        align-items: center;
        gap: 10px;
        
        span {
          font-size: 14px;
          color: #606266;
          white-space: nowrap;
        }
        
        .el-progress {
          width: 120px;
        }
      }
      
      .area-count {
        span {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .drawing-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 15px;
    background: #F8F9FA;
    border: 1px solid #E9ECEF;
    border-radius: 8px;
    
    .tool-group, .action-group {
      .group-title {
        font-size: 12px;
        color: #909399;
        margin-bottom: 10px;
        font-weight: bold;
      }
      
      .tool-buttons, .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
    
    .tool-buttons {
      .el-button {
        padding: 8px 12px;
        
        &.active-tool {
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }
        
        .tool-content {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .tool-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.1);
          }
          
          .tool-label {
            font-size: 13px;
          }
          
          .tool-count {
            font-size: 11px;
            color: #909399;
          }
        }
      }
    }
    
    .action-buttons {
      .el-button {
        padding: 6px 12px;
        font-size: 12px;
        
        i {
          margin-right: 4px;
        }
      }
    }
  }
  
  .map-container {
    margin-bottom: 20px;
    
    .map-content {
      position: relative;
      
      .map-canvas {
        height: 500px;
        border: 2px solid #E4E7ED;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        
        &.readonly {
          border-color: #C0C4CC;
          opacity: 0.8;
        }
        
        .map-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 10px;
          color: #909399;
          z-index: 1000;
          
          span {
            font-size: 14px;
          }
        }
        
        .map-legend {
          position: absolute;
          top: 10px;
          right: 10px;
          width: 280px;
          background: rgba(255, 255, 255, 0.95);
          border: 1px solid #E4E7ED;
          border-radius: 8px;
          overflow: hidden;
          z-index: 1000;
          
          .legend-header {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #F5F7FA;
            border-bottom: 1px solid #E4E7ED;
            
            i {
              color: #409EFF;
              margin-right: 8px;
              font-size: 16px;
            }
            
            .legend-title {
              font-weight: bold;
              color: #303133;
              font-size: 14px;
            }
          }
          
          .legend-content {
            padding: 10px;
            
            .legend-item {
              display: flex;
              align-items: center;
              padding: 10px;
              border-radius: 6px;
              margin-bottom: 8px;
              transition: all 0.3s ease;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              &:hover {
                background: #F0F9FF;
              }
              
              &.active {
                background: #E1F0FF;
                border: 1px solid #409EFF;
              }
              
              .legend-color {
                width: 16px;
                height: 16px;
                border-radius: 4px;
                margin-right: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                flex-shrink: 0;
              }
              
              .legend-info {
                flex: 1;
                
                .legend-label {
                  font-size: 14px;
                  font-weight: bold;
                  color: #303133;
                  margin-bottom: 2px;
                }
                
                .legend-desc {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 4px;
                }
                
                .legend-count {
                  font-size: 11px;
                  color: #409EFF;
                  font-weight: bold;
                }
              }
            }
          }
        }
        
        .operation-tips {
          position: absolute;
          bottom: 10px;
          left: 10px;
          background: rgba(255, 255, 255, 0.95);
          border: 1px solid #E4E7ED;
          border-radius: 6px;
          padding: 10px;
          z-index: 1000;
          
          .tip-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #606266;
            margin-bottom: 5px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            i {
              margin-right: 6px;
              color: #409EFF;
            }
          }
        }
      }
    }
  }
  
  .area-list {
    background: #fff;
    border: 1px solid #E4E7ED;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #E4E7ED;
      background: #F8F9FA;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 14px;
      }
    }
    
    .list-content {
      padding: 15px 20px;
      
      .area-type-group {
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .type-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          
          .type-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
          }
          
          .type-name {
            font-weight: bold;
            color: #303133;
            font-size: 14px;
          }
          
          .type-count {
            margin-left: 8px;
            font-size: 12px;
            color: #909399;
          }
        }
        
        .area-items {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 10px;
          
          .area-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #F8F9FA;
            border: 1px solid #E9ECEF;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              background: #E3F2FD;
              border-color: #409EFF;
              transform: translateY(-1px);
            }
            
            .area-info {
              flex: 1;
              
              .area-name {
                display: block;
                font-size: 13px;
                color: #303133;
                font-weight: bold;
                margin-bottom: 2px;
              }
              
              .area-time {
                font-size: 11px;
                color: #909399;
              }
            }
            
            .area-actions {
              display: flex;
              gap: 4px;
              
              .el-button {
                padding: 2px 4px;
                
                i {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .operation-tips-panel {
    ::v-deep .el-alert__description {
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 5px;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .convection-area-drawing {
    .map-container .map-content .map-canvas .map-legend {
      width: 240px;
    }
  }
}

@media (max-width: 768px) {
  .convection-area-drawing {
    .drawing-header {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
      
      .progress-section {
        align-items: flex-start;
      }
    }
    
    .drawing-toolbar {
      flex-direction: column;
      gap: 15px;
      
      .tool-buttons, .action-buttons {
        justify-content: flex-start;
      }
    }
    
    .map-container .map-content .map-canvas {
      height: 400px;
      
      .map-legend {
        position: relative;
        width: 100%;
        margin-top: 10px;
      }
    }
    
    .area-list .list-content .area-type-group .area-items {
      grid-template-columns: 1fr;
    }
  }
}
</style> 