<template>
  <div class="convection-area-drawing">
    <!-- 头部标题 -->
    <div class="component-header">
      <div class="header-left">
        <h4>第二部分：落区绘制</h4>
        <span class="score-info">(32分)</span>
      </div>
      <div class="header-right">
        <div class="progress-container">
          <span class="progress-label">完成进度：</span>
          <el-progress
            :percentage="areaProgress"
            :stroke-width="8"
            :color="progressColor"
            class="progress-bar"
          />
          <span class="progress-text">{{ areaProgress }}%</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧工具栏 -->
      <div class="left-toolbar">
        <!-- 工具栏标题 -->
        <div class="toolbar-title">
          <i class="el-icon-map-location" />
          <span>绘制工具</span>
        </div>

        <!-- 强对流天气类型选择 -->
        <div class="convection-types">
          <div class="type-title">强对流天气类型</div>
          <div class="type-buttons">
            <div
              v-for="type in convectionTypes"
              :key="type.value"
              :class="{ 'active': selectedType === type.value }"
              :title="type.label + '（' + type.description + '）'"
              class="type-button"
              @click="selectType(type.value)"
            >
              <div :style="{ backgroundColor: type.color }" class="color-indicator" />
              <div class="type-content">
                <i :class="type.icon" class="type-icon" />
                <div class="type-text">
                  <span class="type-name">{{ type.shortLabel }}</span>
                  <span class="type-desc">{{ type.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 绘制控制按钮 -->
        <div class="drawing-controls">
          <el-button
            :disabled="!selectedType || readonly"
            :class="{ 'drawing-active': isDrawing }"
            type="primary"
            size="small"
            class="control-button"
            @click="startDrawing"
          >
            <i :class="isDrawing ? 'el-icon-loading' : 'el-icon-edit'" />
            <span>{{ isDrawing ? '绘制中' : '开始绘制' }}</span>
          </el-button>

          <el-button
            :disabled="!selectedType || !hasDrawnAreas || readonly"
            type="warning"
            size="small"
            class="control-button"
            @click="clearCurrentType"
          >
            <i class="el-icon-delete" />
            <span>清除当前</span>
          </el-button>

          <el-button
            :disabled="!hasAnyDrawnAreas || readonly"
            type="danger"
            size="small"
            class="control-button"
            @click="clearAllAreas"
          >
            <i class="el-icon-refresh-left" />
            <span>清除全部</span>
          </el-button>
        </div>

        <!-- 绘制提示 -->
        <div v-if="isDrawing" class="drawing-tip">
          <i class="el-icon-info" />
          <div class="tip-text">
            <div>点击地图添加点</div>
            <div>双击结束绘制</div>
            <div>绘制{{ getCurrentTypeLabel() }}落区</div>
          </div>
        </div>

        <!-- 站点显示控制 -->
        <div class="station-control-section">
          <div class="station-control-title">
            <i class="el-icon-location" />
            <span>站点显示</span>
          </div>
          <div class="station-control-options">
            <el-checkbox
              v-model="showStations"
              class="station-checkbox"
            >
              <span class="checkbox-text">显示气象站点</span>
            </el-checkbox>
          </div>
        </div>

        <!-- 统计信息 -->
        <div v-if="selectedType" class="stats-info">
          <div class="stats-title">当前类型统计</div>
          <div class="stats-item">
            <span class="stats-label">已绘制落区：</span>
            <span class="stats-value">{{ getAreaCount(selectedType) }}个</span>
          </div>
        </div>

        <!-- 总体统计 -->
        <div v-if="hasAnyDrawnAreas" class="drawing-stats">
          <div class="stats-title">绘制统计</div>
          <div class="stats-content">
            <div v-for="type in drawnTypes" :key="type.value" class="stat-item">
              <div class="stat-label-container">
                <div :style="{ backgroundColor: type.color }" class="stat-color" />
                <span class="stat-label">{{ type.shortLabel }}：</span>
              </div>
              <span class="stat-value">{{ getAreaCount(type.value) }}个</span>
            </div>
            <div class="stat-total">
              <span class="stat-label">总计：</span>
              <span class="stat-value">{{ totalAreaCount }}个落区</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧地图区域 -->
      <div class="map-area">
        <div class="map-container">
          <div id="convection-area-map" class="map-canvas">
            <!-- 地图加载提示 -->
            <div v-if="!mapLoaded" class="map-loading">
              <i class="el-icon-loading" />
              <span>地图加载中...</span>
            </div>

            <!-- 地图图例 -->
            <div v-if="mapLoaded" class="map-legend">
              <div class="legend-title">强对流天气类型图例</div>
              <div class="legend-items">
                <div
                  v-for="type in convectionTypes"
                  :key="type.value"
                  class="legend-item"
                >
                  <div :style="{ backgroundColor: type.color }" class="legend-color" />
                  <i :class="type.icon" class="legend-icon" />
                  <span class="legend-label">{{ type.label }}</span>
                </div>
              </div>
            </div>

            <!-- 操作提示 -->
            <div v-if="mapLoaded && !readonly" class="operation-tips">
              <div class="tip-item">
                <i class="el-icon-mouse" />
                <span>左键单击添加点，双击结束绘制</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-edit-outline" />
                <span>绘制完成后可拖拽节点修改形状</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 只读模式标识 -->
    <div v-if="readonly" class="readonly-mask">
      <div class="readonly-text">
        <i class="el-icon-view" />
        <span>只读模式</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
import 'ol/ol.css'
import { Map, View } from 'ol'
import LayerVector from 'ol/layer/Vector'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'
import SourceVector from 'ol/source/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style'
import Draw from 'ol/interaction/Draw'
import { Feature } from 'ol'
import { Polygon, Point } from 'ol/geom'
import Modify from 'ol/interaction/Modify'
import Snap from 'ol/interaction/Snap'
import { unByKey } from 'ol/Observable'

// 天地图token
const tk = 'ef7e830b6e72f9990997257effbc61c7'

export default {
  name: 'ConvectionAreaDrawing',
  props: {
    // 初始绘制数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 是否只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 站点数据
    stationData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      map: null,
      mapLoaded: false,
      baseLayer: null,
      convectionLayer: null,
      convectionSource: null,
      stationLayer: null,
      stationSource: null,

      // 绘制相关
      isDrawing: false,
      selectedType: '',
      drawInteraction: null,
      modifyInteraction: null,
      snapInteraction: null,
      drawListener: null,
      showStations: true,

      // 强对流天气类型配置
      convectionTypes: [
        {
          label: '短时强降水',
          shortLabel: '强降水',
          description: '20mm/h以上',
          value: 'heavy_rainfall',
          color: '#1890ff',
          icon: 'el-icon-heavy-rain'
        },
        {
          label: '雷暴大风',
          shortLabel: '雷暴风',
          description: '8级以上大风',
          value: 'thunderstorm_wind',
          color: '#52c41a',
          icon: 'el-icon-wind-power'
        },
        {
          label: '冰雹',
          shortLabel: '冰雹',
          description: '2cm以上大冰雹',
          value: 'hail',
          color: '#722ed1',
          icon: 'el-icon-ice-cream-round'
        },
        {
          label: '龙卷',
          shortLabel: '龙卷',
          description: '龙卷风',
          value: 'tornado',
          color: '#f5222d',
          icon: 'el-icon-hurricane'
        }
      ],

      // 绘制数据存储
      drawnAreas: {
        heavy_rainfall: [],
        thunderstorm_wind: [],
        hail: [],
        tornado: []
      }
    }
  },
  computed: {
    ...mapState('convection', {
      examAnswer: state => state.examAnswer
    }),

    areaProgress() {
      return this.examAnswer.areaProgress || 0
    },

    progressColor() {
      const progress = this.areaProgress
      if (progress < 30) return '#f56c6c'
      if (progress < 70) return '#e6a23c'
      return '#67c23a'
    },

    hasDrawnAreas() {
      return this.selectedType && this.drawnAreas[this.selectedType].length > 0
    },

    hasAnyDrawnAreas() {
      return Object.values(this.drawnAreas).some(areas => areas.length > 0)
    },

    drawnTypes() {
      return this.convectionTypes.filter(type =>
        this.drawnAreas[type.value] && this.drawnAreas[type.value].length > 0
      )
    },

    totalAreaCount() {
      return Object.values(this.drawnAreas).reduce((total, areas) => total + areas.length, 0)
    }
  },
  methods: {
    ...mapMutations('convection', [
      'UPDATE_AREA_ANSWER',
      'UPDATE_PROGRESS'
    ]),

    ...mapActions('convection', [
      'autoSave'
    ]),

    // 初始化地图
    initMap() {
      // 底图图层
      this.baseLayer = new TileLayer({
        source: new XYZ({
          url: `http://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tk}`
        })
      })

      // 注记图层
      const annotationLayer = new TileLayer({
        source: new XYZ({
          url: `http://t{0-7}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tk}`
        })
      })

      // 强对流绘制图层
      this.convectionSource = new SourceVector({
        wrapX: false
      })
      this.convectionLayer = new LayerVector({
        source: this.convectionSource,
        style: this.getAreaStyle.bind(this)
      })

      // 站点图层
      this.stationSource = new SourceVector()
      this.stationLayer = new LayerVector({
        source: this.stationSource,
        style: this.getStationStyle.bind(this)
      })

      // 创建地图
      this.map = new Map({
        target: 'convection-area-map',
        layers: [
          this.baseLayer,
          annotationLayer,
          this.convectionLayer,
          this.stationLayer
        ],
        view: new View({
          center: fromLonLat([116.3, 39.9]), // 北京为中心
          zoom: 7,
          minZoom: 5,
          maxZoom: 15
        })
      })

      // 添加修改交互
      this.modifyInteraction = new Modify({
        source: this.convectionSource
      })
      if (!this.readonly) {
        this.map.addInteraction(this.modifyInteraction)
      }

      // 添加对齐交互
      this.snapInteraction = new Snap({
        source: this.convectionSource
      })
      if (!this.readonly) {
        this.map.addInteraction(this.snapInteraction)
      }

      this.mapLoaded = true
      this.loadInitialData()
      this.loadStationData()
    },

    // 选择强对流类型
    selectType(type) {
      if (this.readonly) return

      if (this.isDrawing) {
        this.stopDrawing()
      }
      this.selectedType = type
    },

    // 开始绘制
    startDrawing() {
      if (!this.selectedType || this.readonly) return

      this.isDrawing = true

      this.drawInteraction = new Draw({
        source: this.convectionSource,
        type: 'Polygon',
        style: this.getDrawingStyle()
      })

      this.drawInteraction.on('drawend', (event) => {
        const feature = event.feature
        const geometry = feature.getGeometry()

        // 设置要素属性
        feature.setProperties({
          convectionType: this.selectedType,
          timestamp: Date.now()
        })

        // 保存到数据结构
        const geoJson = new GeoJSON().writeGeometry(geometry)
        this.drawnAreas[this.selectedType].push({
          type: this.selectedType,
          geometry: geoJson,
          timestamp: Date.now()
        })

        this.updateAreaAnswer()
        this.calculateAreaProgress()
        this.stopDrawing()

        // 自动保存
        this.autoSave()
      })

      this.map.addInteraction(this.drawInteraction)
    },

    // 停止绘制
    stopDrawing() {
      if (this.drawInteraction) {
        this.map.removeInteraction(this.drawInteraction)
        this.drawInteraction = null
      }
      this.isDrawing = false
    },

    // 清除当前类型的落区
    clearCurrentType() {
      if (!this.selectedType || this.readonly) return

      // 清除地图上的要素
      const features = this.convectionSource.getFeatures()
      features.forEach(feature => {
        if (feature.get('convectionType') === this.selectedType) {
          this.convectionSource.removeFeature(feature)
        }
      })

      // 清除数据
      this.drawnAreas[this.selectedType] = []
      this.updateAreaAnswer()
      this.calculateAreaProgress()
      this.autoSave()
    },

    // 清除所有落区
    clearAllAreas() {
      if (this.readonly) return

      this.$confirm('确定要清除所有绘制的落区吗？', '确认清除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.convectionSource.clear()
        Object.keys(this.drawnAreas).forEach(key => {
          this.drawnAreas[key] = []
        })
        this.updateAreaAnswer()
        this.calculateAreaProgress()
        this.autoSave()
        this.$message.success('已清除所有落区')
      }).catch(() => {
        // 用户取消
      })
    },

    // 获取落区样式
    getAreaStyle(feature) {
      const convectionType = feature.get('convectionType')
      const typeConfig = this.convectionTypes.find(t => t.value === convectionType)

      if (!typeConfig) return null

      return new Style({
        stroke: new Stroke({
          color: typeConfig.color,
          width: 3
        }),
        fill: new Fill({
          color: typeConfig.color + '30' // 添加透明度
        })
      })
    },

    // 获取绘制时的样式
    getDrawingStyle() {
      const typeConfig = this.convectionTypes.find(t => t.value === this.selectedType)

      return new Style({
        stroke: new Stroke({
          color: typeConfig.color,
          width: 3,
          lineDash: [5, 5] // 虚线表示正在绘制
        }),
        fill: new Fill({
          color: typeConfig.color + '20'
        })
      })
    },

    // 获取站点样式
    getStationStyle() {
      return new Style({
        image: new CircleStyle({
          radius: 4,
          fill: new Fill({
            color: '#1890ff'
          }),
          stroke: new Stroke({
            color: '#ffffff',
            width: 1
          })
        })
      })
    },

    // 获取当前类型标签
    getCurrentTypeLabel() {
      const typeConfig = this.convectionTypes.find(t => t.value === this.selectedType)
      return typeConfig ? typeConfig.shortLabel : ''
    },

    // 获取指定类型的落区数量
    getAreaCount(type) {
      return this.drawnAreas[type] ? this.drawnAreas[type].length : 0
    },

    // 加载初始数据
    loadInitialData() {
      if (this.initialData && typeof this.initialData === 'object') {
        Object.keys(this.initialData).forEach(type => {
          if (this.drawnAreas[type] && Array.isArray(this.initialData[type])) {
            this.drawnAreas[type] = [...this.initialData[type]]

            // 在地图上绘制
            this.initialData[type].forEach(area => {
              try {
                const geometry = new GeoJSON().readGeometry(area.geometry)
                const feature = new Feature(geometry)
                feature.setProperties({
                  convectionType: type,
                  timestamp: area.timestamp
                })
                this.convectionSource.addFeature(feature)
              } catch (error) {
                console.error('加载落区数据失败:', error)
              }
            })
          }
        })
        this.calculateAreaProgress()
      }
    },

    // 加载站点数据
    loadStationData() {
      if (this.stationData && this.stationData.length > 0) {
        this.stationSource.clear()

        this.stationData.forEach(station => {
          if (station.lon && station.lat) {
            const geometry = new Point(fromLonLat([station.lon, station.lat]))
            const feature = new Feature(geometry)
            feature.setProperties(station)
            this.stationSource.addFeature(feature)
          }
        })
      }
    },

    // 更新区域答案到store
    updateAreaAnswer() {
      this.UPDATE_AREA_ANSWER(this.drawnAreas)
    },

    // 计算落区绘制进度
    calculateAreaProgress() {
      const totalTypes = this.convectionTypes.length
      const completedTypes = Object.values(this.drawnAreas).filter(areas => areas.length > 0).length
      const progress = totalTypes > 0 ? Math.round(completedTypes / totalTypes * 100) : 0

      this.UPDATE_PROGRESS({ type: 'area', progress })
    }
  },

  watch: {
    showStations(newVal) {
      if (this.stationLayer) {
        this.stationLayer.setVisible(newVal)
      }
    },

    stationData: {
      handler() {
        this.loadStationData()
      },
      deep: true
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initMap()
    })
  },

  beforeDestroy() {
    if (this.map) {
      this.map.setTarget(null)
    }
  }
}
</script>

<style scoped>
.convection-area-drawing {
  position: relative;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f2f5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left h4 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.score-info {
  color: #52c41a;
  font-size: 16px;
  font-weight: 600;
  background: rgba(82, 196, 26, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.progress-bar {
  width: 120px;
}

.progress-text {
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  min-width: 35px;
}

.main-content {
  display: flex;
  height: 600px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.left-toolbar {
  width: 280px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  padding: 15px;
  overflow-y: auto;
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.convection-types {
  margin-bottom: 20px;
}

.type-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 10px;
}

.type-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.type-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.type-button:hover {
  border-color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.type-button.active {
  border-color: #1890ff;
  background: #f0f9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.color-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.type-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.type-icon {
  font-size: 18px;
  color: #1890ff;
}

.type-text {
  display: flex;
  flex-direction: column;
}

.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.type-desc {
  font-size: 12px;
  color: #8c8c8c;
}

.drawing-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.control-button {
  width: 100%;
  justify-content: flex-start;
}

.control-button.drawing-active {
  background: #52c41a;
  border-color: #52c41a;
}

.drawing-tip {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.drawing-tip i {
  color: #fa8c16;
  font-size: 14px;
  margin-top: 2px;
}

.tip-text div {
  font-size: 12px;
  color: #d46b08;
  line-height: 1.4;
}

.station-control-section {
  margin-bottom: 20px;
}

.station-control-title {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.station-checkbox .checkbox-text {
  font-size: 13px;
  color: #606266;
}

.stats-info, .drawing-stats {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 15px;
}

.stats-title {
  font-size: 13px;
  font-weight: 600;
  color: #52c41a;
  margin-bottom: 8px;
}

.stats-item, .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.stat-label-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.stat-color {
  width: 8px;
  height: 8px;
  border-radius: 2px;
}

.stats-label, .stat-label {
  color: #606266;
}

.stats-value, .stat-value {
  color: #303133;
  font-weight: 600;
}

.stat-total {
  display: flex;
  justify-content: space-between;
  padding-top: 6px;
  border-top: 1px solid #d9f7be;
  font-weight: 600;
  color: #52c41a;
}

.map-area {
  flex: 1;
  position: relative;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 10px;
  color: #1890ff;
  font-size: 14px;
}

.map-legend {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.legend-title {
  font-size: 12px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #e8e8e8;
}

.legend-icon {
  font-size: 12px;
  color: #606266;
}

.legend-label {
  font-size: 11px;
  color: #606266;
}

.operation-tips {
  position: absolute;
  bottom: 15px;
  left: 15px;
  background: rgba(0, 0, 0, 0.75);
  color: #ffffff;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  z-index: 1000;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.readonly-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  border-radius: 12px;
}

.readonly-text {
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 10px 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}
</style>
