<template>
  <div>
    <div id="picture-map">
      <div id="map">
        <div class="draw-menu">
          <div class="menu-title">天气落区绘制工具</div>
          <!-- 天气类型选择 -->
          <div class="menu-item">
            <div class="item-title">天气类型</div>
            <div class="item-content">
              <select v-model="weatherType" @change="handleWeatherTypeChange">
                <option v-for="option in weatherTypes" :key="option.value" :value="option.value">{{ option.label }}</option>
              </select>
            </div>
          </div>

          <!-- 天气等级选择 -->
          <div class="menu-item">
            <div class="item-title">天气等级</div>
            <div class="weather-levels">
              <div
                v-for="level in weatherLevels"
                :key="level.value"
                :class="{ 'active': weatherLevel === level.value }"
                class="weather-level-item"
                @click="selectWeatherLevel(level.value)"
              >
                <span class="level-name">{{ level.label }}</span>
                <div :style="{ backgroundColor: level.color }" class="color-block" />
              </div>
            </div>
          </div>

          <!-- 控制按钮 -->
          <div class="menu-item">
            <button
              :class="{ 'active': isDrawing }"
              class="draw-btn"
              @click="startDrawing"
            >
              {{ isDrawing ? '正在绘制...' : '绘制天气落区' }}
            </button>
            <div v-if="isDrawing" class="draw-tip">
              点击地图添加点，形成平滑曲线，双击结束绘制
            </div>

            <!-- 性能优化选项 -->
            <div class="performance-option">
              <label>
                <input v-model="enableOverlapProcessing" type="checkbox">
                启用重叠区域处理（如性能较差可关闭）
              </label>
            </div>
            <!-- 修改清除按钮为删除图层 -->
            <!-- <button
                            class="clear-btn"
                            @click="deleteWeatherFeature"
                            :class="{ 'active': isDeleting }"
                        >
                            {{ isDeleting ? '选择要删除的落区...' : '删除图层' }}
                        </button> -->
            <!-- 添加清除所有按钮 -->
            <button
              class="clear-all-btn"
              @click="clearWeatherAreas"
            >
              清除所有落区
            </button>
          </div>

          <!-- 添加叠加站点按钮 -->
          <div class="menu-item">
            <button class="add-station-btn" @click="openStationDialog">叠加站点</button>
          </div>

          <!-- 生成按钮 -->
          <div class="menu-item">
            <button class="generate-btn" @click="generateImage">生成图片</button>
          </div>
        </div>
      </div>

      <!-- 预览对话框 -->
      <div v-if="showPreview" class="preview-dialog">
        <div class="preview-content">
          <div class="preview-header">
            <h3>图片预览</h3>
            <h3 style="color: #fd0000;font-size: 18px;">拖动图例、标题、副标题、落款可调整位置</h3>
            <button class="close-btn" @click="closePreview">×</button>
          </div>
          <div class="preview-body">
            <div ref="previewContainer" class="preview-canvas-container">
              <canvas ref="previewCanvas" />
              <div
                v-if="previewTitle"
                ref="previewTitle"
                :key="'preview-title-' + (previewTitle || 'default')"
                :style="{
                  left: titlePosition.x + 'px',
                  top: titlePosition.y + 'px',
                  fontSize: titleStyle.fontSize + 'px',
                  fontFamily: formatFontFamily(titleStyle.fontFamily),
                  color: titleStyle.color,
                  fontWeight: 'bold'
                }"
                class="preview-title"
                @mousedown="startDrag($event, 'title')"
              >
                {{ previewTitle }}
              </div>

              <div
                v-if="previewSubtitle"
                ref="previewSubtitle"
                :key="'preview-subtitle-' + (previewSubtitle || 'default')"
                :style="{
                  left: subtitlePosition.x + 'px',
                  top: subtitlePosition.y + 'px',
                  fontSize: subtitleStyle.fontSize + 'px',
                  fontFamily: formatFontFamily(subtitleStyle.fontFamily),
                  color: subtitleStyle.color
                }"
                class="preview-subtitle"
                @mousedown="startDrag($event, 'subtitle')"
              >
                {{ previewSubtitle }}
              </div>
              <div
                v-if="previewShowLegend"
                :style="{
                  left: legendPosition.x + 'px',
                  top: legendPosition.y + 'px',
                  backgroundColor: legendStyle.backgroundColor
                }"
                class="draggable-legend"
                @mousedown="startDrag($event, 'legend')"
              >
                <div
                  :style="{
                    fontSize: drawnWeatherTypes.length > 1 ? (legendStyle.titleFontSize * 0.7) + 'px' : legendStyle.titleFontSize + 'px',
                    fontFamily: formatFontFamily(legendStyle.fontFamily),
                    color: legendStyle.color
                  }"
                  class="legend-title"
                >图例</div>
                <div class="legend-items">
                  <!-- 循环显示所有已绘制天气类型的图例 -->
                  <div v-for="(type, typeIndex) in drawnWeatherTypes" :key="'type-'+typeIndex">
                    <!-- 天气类型标题 -->
                    <div
                      :style="{
                        fontSize: legendStyle.itemFontSize + 'px',
                        fontFamily: formatFontFamily(legendStyle.fontFamily),
                        color: legendStyle.color,
                        fontWeight: 'bold',
                        marginTop: typeIndex > 0 ? '10px' : '0'
                      }"
                      class="legend-type-title"
                    >
                      {{ getWeatherTypeName(type) }}
                    </div>
                    <!-- 天气等级 -->
                    <div
                      v-for="(level, levelIndex) in weatherLevelConfig[type]"
                      :key="'level-'+type+'-'+levelIndex"
                      :style="{
                        fontSize: legendStyle.itemFontSize + 'px',
                        fontFamily: formatFontFamily(legendStyle.fontFamily),
                        color: legendStyle.color
                      }"
                      class="legend-item"
                    >
                      <div
                        :style="{
                          backgroundColor: level.color,
                          width: '15px',
                          height: '15px',
                          border: '1px solid #ddd',
                          backgroundImage: type === 'sleet' ? 'linear-gradient(135deg, white 25%, transparent 25%, transparent 50%, white 50%, white 75%, transparent 75%)' : 'none',
                          backgroundSize: '6px 6px'
                        }"
                        class="legend-color"
                      />
                      <span>{{ level.label }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="previewShowSignature"
                ref="previewSignature"
                :key="'preview-signature-' + (signatureText || 'default')"
                :style="{
                  left: signaturePosition.x + 'px',
                  top: signaturePosition.y + 'px',
                  fontSize: signatureStyle.fontSize + 'px',
                  fontFamily: signatureStyle.fontFamily,
                  color: signatureStyle.color
                }"
                class="draggable-signature"
                @mousedown="startDrag($event, 'signature')"
              >
                {{ signatureText }}
              </div>
              <!-- 审图号 -->
              <div
                v-if="showMapApprovalNumber"
                ref="approvalNumber"
                :key="'preview-approval-' + (mapApprovalNumber || 'default')"
                :style="{
                  left: mapApprovalNumberPosition.x + 'px',
                  top: mapApprovalNumberPosition.y + 'px',
                  fontSize: mapApprovalNumberStyle.fontSize + 'px',
                  fontFamily: mapApprovalNumberStyle.fontFamily,
                  color: mapApprovalNumberStyle.color
                }"
                class="draggable-approval-number"
                @mousedown="startDrag($event, 'approvalNumber')"
              >
                {{ mapApprovalNumber }}
              </div>

              <!-- 发布时间 -->
              <div
                v-if="showPublishTime"
                ref="publishTime"
                :key="'preview-publishtime-' + (publishTime || 'default')"
                :style="{
                  left: publishTimePosition.x + 'px',
                  top: publishTimePosition.y + 'px',
                  fontSize: publishTimeStyle.fontSize + 'px',
                  fontFamily: publishTimeStyle.fontFamily,
                  color: publishTimeStyle.color
                }"
                class="draggable-publish-time"
                @mousedown="startDrag($event, 'publishTime')"
              >
                {{ publishTime }}
              </div>

              <!-- 优化站点显示内容 -->
              <div
                v-for="(item, index) in customTexts"
                :key="'text-' + index"
                :class="{ 'selected': selectedCustomText === index, 'station-marker': item.style.isStation }"
                :style="{
                  left: item.position.x + 'px',
                  top: item.position.y + 'px',
                  fontSize: item.style.fontSize + 'px',
                  fontFamily: formatFontFamily(item.style.fontFamily),
                  color: item.style.color
                }"
                class="draggable-custom-text"
                @mousedown="startDrag($event, 'customText', index)"
                @click.stop="selectCustomText(index)"
              >
                <div v-if="item.style.isStation" class="station-dot" />
                {{ item.content }}
                <div v-if="item.style.isStation" class="station-id">{{ item.style.stationId }}</div>
              </div>

              <!-- 添加的图片元素 -->
              <div
                v-for="(item, index) in canvasImages"
                :key="'image-' + index"
                :class="{ 'selected': selectedCanvasImage === index }"
                :style="{
                  left: item.position.x + 'px',
                  top: item.position.y + 'px',
                  width: item.size + 'px',
                  height: 'auto',
                  transform: `rotate(${item.rotation || 0}deg)`,
                  filter: item.colorStrength > 0 ? `opacity(${(100-item.colorStrength)/100}) drop-shadow(0 0 0 ${item.color}) saturate(${item.colorStrength/20 + 1})` : 'none'
                }"
                class="draggable-image"
                @mousedown="startDrag($event, 'image', index)"
                @click.stop="selectCanvasImage(index)"
              >
                <img
                  :src="item.src"
                  :alt="item.name"
                  :style="{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                  }"
                >
              </div>

              <!-- 移除HTML站点标记，完全使用canvas绘制 -->
            </div>

            <div class="preview-controls">
              <!-- 标题设置 -->
              <div class="control-group">
                <!-- <h3>标题设置</h3> -->
                <div class="title-text">
                  <div class="ivu-form-item-label">主标题</div>
                  <Input
                    :value="previewTitle"
                    type="text"
                    placeholder="请输入主标题"
                    title="输入主要标题信息"
                    style="width: 100%"
                    @input="event => handleInputChange('previewTitle', event.target ? event.target.value : event)"
                  />

                </div>
                <div class="size-slider">
                  <input v-model.number="titleStyle.fontSize" type="range" min="12" max="48" class="slider" @input="updatePreviewImageImmediate">
                  <!-- <span class="value-display">{{ titleStyle.fontSize }}px</span> -->
                </div>
              </div>

              <!-- 副标题设置 -->
              <div class="control-group">
                <!-- <h3>副标题设置</h3> -->
                <div class="title-text">
                  <div class="ivu-form-item-label">副标题</div>
                  <Input
                    :value="previewSubtitle"
                    type="text"
                    placeholder="请输入副标题内容"
                    title="输入副标题，将显示在主标题下方"
                    style="width: 100%"
                    @input="event => handleInputChange('previewSubtitle', event.target ? event.target.value : event)"
                  />

                </div>
                <div class="size-slider">
                  <input v-model.number="subtitleStyle.fontSize" type="range" min="10" max="36" class="slider" @input="updatePreviewImageImmediate">
                  <!-- <span class="value-display">{{ subtitleStyle.fontSize }}px</span> -->
                </div>
              </div>

              <!-- 落款设置 -->
              <div class="control-group">
                <!-- <h3>落款设置</h3> -->
                <div class="title-text">
                  <div class="ivu-form-item-label">落款</div>
                  <Input
                    :value="signatureText"
                    type="text"
                    placeholder="请输入落款信息"
                    title="输入落款信息，通常为单位名称"
                    style="width: 100%"
                    @input="event => handleInputChange('signatureText', event.target ? event.target.value : event)"
                  />

                </div>
                <div class="size-slider">
                  <input v-model.number="signatureStyle.fontSize" type="range" min="10" max="36" class="slider" @input="updatePreviewImageImmediate">
                  <!-- <span class="value-display">{{ signatureStyle.fontSize }}px</span> -->
                </div>
              </div>

              <!-- 批准文号设置 -->
              <div class="control-group">
                <!-- <h3>批准文号设置</h3> -->
                <div class="title-text">
                  <div class="ivu-form-item-label">批准文号</div>
                  <Input
                    :value="mapApprovalNumber"
                    type="text"
                    placeholder="请输入批准文号"
                    title="输入审批文号信息"
                    style="width: 100%"
                    @input="event => handleInputChange('mapApprovalNumber', event.target ? event.target.value : event)"
                  />

                </div>
                <div class="size-slider">
                  <input v-model.number="mapApprovalNumberStyle.fontSize" type="range" min="8" max="24" class="slider" @input="updatePreviewImageImmediate">
                  <!-- <span class="value-display">{{ mapApprovalNumberStyle.fontSize }}px</span> -->
                </div>
              </div>

              <!-- 发布时间设置 -->
              <div class="control-group">
                <!-- <h3>发布时间设置</h3> -->
                <div class="title-text">
                  <div class="ivu-form-item-label">发布时间</div>
                  <Input
                    :value="publishTime"
                    type="text"
                    placeholder="选择发布时间"
                    title="选择图片发布的日期和时间"
                    style="width: 100%"
                    @input="event => handleInputChange('publishTime', event.target ? event.target.value : event)"
                  />

                </div>
                <div class="size-slider">
                  <input v-model.number="publishTimeStyle.fontSize" type="range" min="8" max="24" class="slider" @input="updatePreviewImageImmediate">
                </div>
              </div>

              <div class="text-action-buttons">
                <!-- 删除这个叠加站点按钮 -->
                <!-- <button class="add-text-btn" @click="openStationDialog">叠加站点</button> -->

              </div>
              <!-- 文字功能 -->
              <div class="control-group style-group">
                <div class="style-title">自定义文字</div>

                <!-- 按钮组 -->
                <div class="text-action-buttons">
                  <button class="add-text-btn" @click="addCustomText">添加文字</button>
                </div>

                <!-- 自定义文字列表 -->
                <div v-if="customTexts.length > 0" class="custom-texts-container">
                  <div v-for="(item, index) in customTexts" :key="'control-' + index" class="custom-text-control">
                    <!-- 文字内容单独一行 -->
                    <div class="text-content-row">
                      <input v-model="item.content" type="text" placeholder="请输入自定义文字内容" class="full-width-input" title="输入自定义文字，可拖动到画布任意位置" @input="updatePreviewImage">
                    </div>

                    <!-- 控制选项占一行 -->
                    <div class="text-style-row">
                      <div class="color-control">
                        <input v-model="item.style.color" type="color" title="选择文字颜色" @input="updatePreviewImageImmediate">
                      </div>

                      <div class="size-control">
                        <el-input-number
                          v-model="item.style.fontSize"
                          :min="10"
                          :max="36"
                          :step="1"
                          size="small"
                          controls-position="right"
                          @change="updatePreviewImageImmediate"
                        />
                      </div>

                      <div class="delete-control">
                        <button class="remove-text-btn" title="删除" @click="removeCustomText(index)">×</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 图片素材库 -->
              <div class="control-group style-group">
                <div class="style-title">图片素材</div>
                <div class="image-gallery">
                  <div
                    v-for="(image, index) in drawImages"
                    :key="index"
                    :class="{ 'active': selectedImage === image }"
                    :title="image.name"
                    class="gallery-item"
                    @click="selectImage(image)"
                  >
                    <img :src="image.src" :alt="image.name">
                    <div class="image-name-tooltip">{{ image.name }}</div>
                  </div>
                </div>
                <div v-if="selectedImage" class="image-controls">
                  <button class="add-image-btn" @click="addImageToCanvas">添加到画布</button>
                </div>
              </div>

              <!-- 已添加图片控制面板 -->
              <div v-if="selectedCanvasImage !== null" class="control-group style-group">
                <div class="style-title">图片属性调整 <button class="close-panel-btn" @click="selectedCanvasImage = null">×</button></div>
                <div class="image-edit-panel">
                  <div class="image-edit-row">
                    <span class="slider-label">旋转角度:</span>
                    <input
                      v-model.number="canvasImages[selectedCanvasImage].rotation"
                      type="range"
                      min="0"
                      max="360"
                      class="slider"
                      title="拖动滑块调整图片旋转角度，范围0-360度"
                      @input="updatePreviewImageImmediate"
                    >
                    <span class="value-display">{{ canvasImages[selectedCanvasImage].rotation }}°</span>
                  </div>
                  <div class="image-edit-row">
                    <span class="slider-label">图片尺寸:</span>
                    <input
                      v-model.number="canvasImages[selectedCanvasImage].size"
                      type="range"
                      min="20"
                      max="300"
                      class="slider"
                      title="拖动滑块调整图片尺寸大小，范围20-300像素"
                      @input="updatePreviewImageImmediate"
                    >
                    <span class="value-display">{{ canvasImages[selectedCanvasImage].size }}px</span>
                  </div>

                  <div class="image-edit-row">
                    <input
                      v-model="canvasImages[selectedCanvasImage].color"
                      type="color"
                      class="color-picker"
                      title="选择滤镜颜色"
                      @input="updatePreviewImageImmediate"
                    >
                    <span class="slider-label">颜色强度:</span>
                    <input
                      v-model.number="canvasImages[selectedCanvasImage].colorStrength"
                      type="range"
                      min="0"
                      max="100"
                      class="slider"
                      title="调整滤镜强度"
                      @input="updatePreviewImageImmediate"
                    >
                    <span class="value-display">{{ canvasImages[selectedCanvasImage].colorStrength }}%</span>
                  </div>
                  <div class="image-edit-row">
                    <button class="delete-btn" @click="deleteImage(selectedCanvasImage)">删除图片</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="preview-footer">
            <button class="cancel-btn" @click="closePreview">取消</button>
            <button class="download-btn" @click="downloadImage">下载图片</button>
          </div>
        </div>
      </div>

      <!-- 站点弹窗 -->
      <el-dialog
        :visible.sync="stationDialogVisible"
        :modal-append-to-body="false"
        :z-index="11000"
        :before-close="handleCloseStationDialog"
        title="叠加站点"
        width="750px"
        custom-class="station-dialog"
        append-to-body
      >
        <div class="station-dialog-content">
          <!-- 搜索和筛选区域 -->
          <div class="search-filter-bar">
            <el-input
              v-model="stationSearchValue"
              placeholder="输入站点名称或编号搜索"
              prefix-icon="el-icon-search"
              clearable
              class="station-search-input"
              @input="filterStations"
            />

            <el-select
              v-model="stationFilterValue"
              placeholder="按旗县筛选"
              clearable
              class="station-filter-select"
              @change="filterStations"
            >
              <el-option
                v-for="item in countyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 表格区域 -->
          <el-table
            ref="stationTable"
            v-loading="stationLoading"
            :data="filteredStationList"
            style="width: 100%"
            height="400px"
            border
            stripe
            highlight-current-row
            @selection-change="handleStationSelectionChange"
          >
            <el-table-column type="selection" width="50" />
            <el-table-column prop="stationName" label="站点名称" min-width="160">
              <template slot-scope="scope">
                <span class="station-name-cell">{{ scope.row.stationName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="stationIdC" label="站号" width="120">
              <template slot-scope="scope">
                <el-tag size="small" type="info">{{ scope.row.stationIdC }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cnty" label="所属旗县" min-width="120">
              <template slot-scope="scope">
                <el-tag size="small" type="success">{{ scope.row.cnty }}</el-tag>
              </template>
            </el-table-column>
          </el-table>

          <!-- 无数据提示 -->
          <div v-if="filteredStationList.length === 0 && !stationLoading" class="empty-tip">
            暂无符合条件的站点数据
          </div>

          <!-- 分页与选择提示 -->
          <div class="selection-info">
            <span v-if="selectedStations.length > 0">
              已选择 <span class="selected-count">{{ selectedStations.length }}</span> 个站点
            </span>
          </div>
        </div>

        <!-- 底部按钮 -->
        <span slot="footer" class="dialog-footer">
          <el-button v-if="selectedStations.length > 0" plain @click="clearStationSelection">清空选择</el-button>
          <el-button @click="stationDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="addStationsToMap">
            <i class="el-icon-plus" /> {{ selectedStations.length > 0 ? '添加站点' : '清除所有站点' }}
          </el-button>
        </span>
      </el-dialog>

      <!-- 在选中的站点显示附加信息 -->
      <div v-if="selectedStation !== null" class="control-group style-group">
        <div class="style-title">站点信息 <button class="close-panel-btn" @click="selectedStation = null">×</button></div>
        <div class="station-info-panel">
          <div class="info-row">
            <span class="info-label">站点名称:</span>
            <span class="info-value">{{ mapStations[selectedStation].name }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">站号:</span>
            <span class="info-value">{{ mapStations[selectedStation].id }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">所属旗县:</span>
            <span class="info-value">{{ mapStations[selectedStation].cnty }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">文字大小:</span>
            <div class="size-control">
              <el-input-number
                v-model="mapStations[selectedStation].style.fontSize"
                :min="10"
                :max="24"
                :step="1"
                size="small"
                controls-position="right"
                @change="updatePreviewImage"
              />
            </div>
          </div>
          <div class="info-row">
            <span class="info-label">文字颜色:</span>
            <div class="color-control">
              <input
                v-model="mapStations[selectedStation].style.color"
                type="color"
                class="color-picker"
                title="选择文字颜色"
                @input="updatePreviewImage"
              >
            </div>
          </div>
          <div class="action-row">
            <button class="remove-station-btn" @click="removeStation(selectedStation)">删除站点</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'ol/ol.css'
import { Map, View } from 'ol'
import LayerVector from 'ol/layer/Vector'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ'
import { fromLonLat } from 'ol/proj'
import SourceVector from 'ol/source/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { Style, Stroke, Fill, Circle as CircleStyle, Text } from 'ol/style'
import { getCookie } from '../../utils/auth'
import * as turf from '@turf/turf'
import { cityInfo } from '../../api/login'
import Draw from 'ol/interaction/Draw'
import { Feature } from 'ol'
import { Polygon, Point } from 'ol/geom'
import Modify from 'ol/interaction/Modify'
import Snap from 'ol/interaction/Snap'
import { getArea } from 'ol/sphere'
import { unByKey } from 'ol/Observable'
import { defaults } from 'ol/interaction'
import { stationByAreaAndStalvel } from '../../api/cimiss'

const tk = 'ef7e830b6e72f9990997257effbc61c7'
const baseMapDict = {
  nw: {
    vec_w: 'http://***********:81/DataServer?T=vec_w&x={x}&y={y}&l={z}',
    img_w: 'http://***********:81/DataServer?T=img_w&x={x}&y={y}&l={z}',
    ter_w: 'http://***********:81/DataServer?T=ter_w&x={x}&y={y}&l={z}'
  },
  net: {
    vec_w:
            'http://t4.tianditu.com/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    img_w:
            'http://t4.tianditu.com/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    ter_w:
            'http://t4.tianditu.com/DataServer?T=ter_w&x={x}&y={y}&l={z}&tk=' +
            tk
  }
}
const annotationDict = {
  nw: {
    vec_w: 'http://***********:81/DataServer?T=cva_w&x={x}&y={y}&l={z}',
    img_w: 'http://***********:81/DataServer?T=cia_w&x={x}&y={y}&l={z}',
    ter_w: 'http://***********:81/DataServer?T=cta_w&x={x}&y={y}&l={z}'
  },
  net: {
    vec_w:
            'http://t4.tianditu.com/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    img_w:
            'http://t4.tianditu.com/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    ter_w:
            'http://t4.tianditu.com/DataServer?T=cta_w&x={x}&y={y}&l={z}&tk=' +
            tk
  }
}

export default {
  name: 'Picture',
  data() {
    return {
      map: null,
      boundaryVector1: null, // 边界
      boundaryVector0: null, // 内部旗县边界
      boundaryVector2: null, // 警戒区
      boundaryVector3: null, // 监视区
      cityName: '',

      // 绘图菜单相关数据
      weatherType: 'precipitation', // 默认选择降水
      weatherTypes: [
        { label: '降水', value: 'precipitation' },
        { label: '雨夹雪', value: 'sleet' },
        { label: '降雪', value: 'snow' },
        { label: '大风', value: 'wind' },
        { label: '沙尘', value: 'sandstorm' },
        { label: '霜冻', value: 'frost' },
        { label: '寒潮', value: 'cooling' },
        { label: '高温', value: 'high_temperature' },
        { label: '强对流', value: 'strong_convection' }
      ],
      weatherLevel: '', // 当前选择的天气等级
      weatherLevels: [], // 天气等级选项，根据天气类型动态变化

      // 图片配置
      // imageTitle: '', // 移除重复定义的图片标题
      // imageSubtitle: '', // 移除重复定义的图片副标题
      showSignature: true, // 是否显示落款
      signatureText: '内蒙古气象台', // 落款文字
      showLegend: true, // 是否显示图例
      showPublishTime: true, // 是否显示发布时间
      publishTime: '', // 发布时间
      publishTimePosition: { x: 0, y: 0 }, // 发布时间位置

      // 绘制相关
      isDrawing: false, // 是否正在绘制
      drawInteraction: null, // 绘制交互
      modifyInteraction: null, // 修改交互
      snapInteraction: null, // 捕捉交互
      weatherLayer: null, // 天气图层
      weatherSource: null, // 天气图层数据源
      drawListener: null, // 绘制监听器

      // 天气等级配置
      weatherLevelConfig: {
        precipitation: [
          { label: '小雨（0.1～9.9mm）', value: 1, color: '#A6F28E' },
          { label: '中雨（10.0～24.9mm）', value: 2, color: '#3DB93D' },
          { label: '大雨（25.0～49.9mm）', value: 3, color: '#61B8FF' },
          { label: '暴雨（50.0～99.9mm）', value: 4, color: '#0000FE' },
          { label: '大暴雨(≥100.0mm)', value: 5, color: '#FA01F3' }
        ],
        sleet: [
          { label: '小雨夹雪（0.1～9.9mm）', value: 1, color: '#9CF28B' },
          { label: '中雨夹雪（10.0～24.9mm）', value: 2, color: '#30B23E' },
          { label: '大雨夹雪（≥25.0mm）', value: 3, color: '#5FAFEF' }
        ],
        snow: [
          { label: '小雪（0.1～2.4mm）', value: 1, color: '#CCCCCC' },
          { label: '中雪（2.5～4.9mm）', value: 2, color: '#A1A1A1' },
          { label: '大雪（5.0～9.9mm）', value: 3, color: '#707070' },
          { label: '暴雪(≥10.0mm)', value: 4, color: '#454545' }
        ],
        wind: [
          { label: '6级(10.8～13.8m/s)', value: 0, color: '#9BBCE9' },
          { label: '7级(13.9～17.1m/s)', value: 1, color: '#6C8CE1' },
          { label: '8级(17.2～20.7m/s)', value: 2, color: '#2B5CC2' },
          { label: '9级(20.8～24.4m/s)', value: 3, color: '#061E78' },
          { label: '10级(24.5～28.4m/s)', value: 4, color: '#461980' },
          { label: '10级以上(≥28.5m/s)', value: 5, color: '#861589' }
        ],
        sandstorm: [
          { label: '扬沙或浮尘', value: 0, color: '#F5D775' },
          { label: '沙尘暴', value: 1, color: '#DA9A30' },
          { label: '强沙尘暴', value: 2, color: '#9E6226' }
        ],
        frost: [
          { label: '2℃～4℃', value: 0, color: '#97D6C3' },
          { label: '0℃～2℃', value: 1, color: '#9BBCE9' },
          { label: '-2℃～0℃', value: 2, color: '#6197E1' },
          { label: '-4℃～-2℃', value: 3, color: '#3C7EDC' },
          { label: '-6℃～-4℃', value: 4, color: '#2657B3' }
        ],
        cooling: [
          { label: '降温0℃~4℃', value: 0, color: '#BBF0FF' },
          { label: '降温4℃~6℃', value: 1, color: '#9FCFFF' },
          { label: '降温6℃~8℃', value: 2, color: '#76B1FD' },
          { label: '降温8℃～10℃', value: 3, color: '#2A9CFF' },
          { label: '降温10℃～12℃', value: 4, color: '#006DFA' },
          { label: '降温12℃～14℃', value: 5, color: '#0102FD' },
          { label: '降温14℃～16℃', value: 6, color: '#AA01E4' },
          { label: '降温16℃以上', value: 7, color: '#4A0174' }
        ],
        high_temperature: [
          { label: '30℃～37℃', value: 0, color: '#FFBFBF' },
          { label: '37℃～40℃', value: 1, color: '#FF5500' },
          { label: '≥40℃', value: 2, color: '#E60000' }
        ],
        strong_convection: [
          { label: '雷暴', value: 0, color: '#FFD0A6' },
          { label: '短时强降水', value: 1, color: '#4AE700' },
          { label: '雷雨大风或冰雹', value: 2, color: '#FD7F7E' }
        ]
      },

      // 预览相关数据
      showPreview: false,
      previewCanvas: null,
      previewCtx: null,
      previewTitle: '内蒙古降水预报图', // 设置合理的默认值
      previewSubtitle: '预报时间：待设置', // 设置默认副标题
      previewShowLegend: true,
      previewShowSignature: true,
      firstLoad: true, // 是否首次加载预览

      // 拖拽相关数据
      dragging: false,
      dragType: null,
      dragStartX: 0,
      dragStartY: 0,
      imagePosition: { x: 0, y: 0 },
      titlePosition: { x: 0, y: 40 },
      subtitlePosition: { x: 0, y: 70 },
      legendPosition: { x: 20, y: 100 },
      signaturePosition: { x: 0, y: 0 }, // 落款位置

      // 样式设置
      titleStyle: {
        fontSize: 24,
        fontFamily: 'SimHei',
        color: '#000000',
        fontWeight: 'bold'
      },
      subtitleStyle: {
        fontSize: 18,
        fontFamily: 'SimHei',
        color: '#000000'
      },
      signatureStyle: {
        fontSize: 18,
        fontFamily: 'SimHei',
        color: '#000000'
      },
      legendStyle: {
        titleFontSize: 18,
        itemFontSize: 14,
        fontFamily: 'SimHei',
        color: '#000000',
        backgroundColor: 'rgba(255, 255, 255, 0)',
        backgroundOpacity: 0.7
      },

      // 可选字体
      fontOptions: [
        { label: '宋体', value: 'SimSun' },
        { label: '黑体', value: 'SimHei' },
        { label: '微软雅黑', value: 'Microsoft YaHei' },
        { label: '楷体', value: 'KaiTi' },
        { label: 'Arial', value: 'Arial' },
        { label: 'Times New Roman', value: 'Times New Roman' }
      ],

      // 临时存储生成的图片数据
      generatedImageData: null,

      // 自定义文字
      customTexts: [],

      // 图片相关
      drawImages: [
        {
          name: '指南针',
          src: require('@/assets/images/draw/north.png'),
          path: 'src/assets/images/draw/north.png'
        }, {
          name: '2-3级风',
          src: require('@/assets/images/draw/2-3级风.png'),
          path: 'src/assets/images/draw/2-3级风.png'
        },
        {
          name: '3-4级风',
          src: require('@/assets/images/draw/3-4级风.png'),
          path: 'src/assets/images/draw/3-4级风.png'
        },
        {
          name: '4-5级风',
          src: require('@/assets/images/draw/4-5级风.png'),
          path: 'src/assets/images/draw/4-5级风.png'
        },
        {
          name: '5-6级风',
          src: require('@/assets/images/draw/5-6级风.png'),
          path: 'src/assets/images/draw/5-6级风.png'
        },
        {
          name: '中雨',
          src: require('@/assets/images/draw/中雨.png'),
          path: 'src/assets/images/draw/中雨.png'
        },
        {
          name: '中雪',
          src: require('@/assets/images/draw/中雪.png'),
          path: 'src/assets/images/draw/中雪.png'
        },
        {
          name: '多云',
          src: require('@/assets/images/draw/多云.png'),
          path: 'src/assets/images/draw/多云.png'
        },
        {
          name: '大雨',
          src: require('@/assets/images/draw/大雨.png'),
          path: 'src/assets/images/draw/大雨.png'
        },
        {
          name: '大雪',
          src: require('@/assets/images/draw/大雪.png'),
          path: 'src/assets/images/draw/大雪.png'
        },
        {
          name: '小雨',
          src: require('@/assets/images/draw/小雨.png'),
          path: 'src/assets/images/draw/小雨.png'
        },
        {
          name: '小雪',
          src: require('@/assets/images/draw/小雪.png'),
          path: 'src/assets/images/draw/小雪.png'
        },
        {
          name: '扬沙',
          src: require('@/assets/images/draw/扬沙.png'),
          path: 'src/assets/images/draw/扬沙.png'
        },
        {
          name: '晴',
          src: require('@/assets/images/draw/晴.png'),
          path: 'src/assets/images/draw/晴.png'
        },
        {
          name: '暴雨',
          src: require('@/assets/images/draw/暴雨.png'),
          path: 'src/assets/images/draw/暴雨.png'
        },
        {
          name: '暴雪',
          src: require('@/assets/images/draw/暴雪.png'),
          path: 'src/assets/images/draw/暴雪.png'
        },
        {
          name: '沙尘暴',
          src: require('@/assets/images/draw/沙尘暴.png'),
          path: 'src/assets/images/draw/沙尘暴.png'
        },
        {
          name: '浮尘',
          src: require('@/assets/images/draw/浮尘.png'),
          path: 'src/assets/images/draw/浮尘.png'
        },
        {
          name: '轻雾',
          src: require('@/assets/images/draw/轻雾.png'),
          path: 'src/assets/images/draw/轻雾.png'
        },
        {
          name: '阴',
          src: require('@/assets/images/draw/阴.png'),
          path: 'src/assets/images/draw/阴.png'
        },
        {
          name: '阵雨',
          src: require('@/assets/images/draw/阵雨.png'),
          path: 'src/assets/images/draw/阵雨.png'
        },
        {
          name: '雷暴',
          src: require('@/assets/images/draw/雷暴.png'),
          path: 'src/assets/images/draw/雷暴.png'
        },
        {
          name: '雾',
          src: require('@/assets/images/draw/雾.png'),
          path: 'src/assets/images/draw/雾.png'
        },
        {
          name: '霾',
          src: require('@/assets/images/draw/霾.png'),
          path: 'src/assets/images/draw/霾.png'
        }
      ],
      selectedImage: null,
      selectedCanvasImage: null, // 当前选中的画布上的图片索引
      imageSize: 60,
      canvasImages: [],

      // 性能优化开关
      enableOverlapProcessing: true, // 是否启用重叠区域处理（默认开启）

      // 审图号相关
      mapApprovalNumber: '底图审图号:蒙S(2019)33号',
      showMapApprovalNumber: true,
      mapApprovalNumberPosition: { x: 0, y: 0 }, // 初始值会在预览时动态设置
      mapApprovalNumberStyle: {
        fontSize: 16,
        fontFamily: 'SimHei',
        color: '#000000'
      },
      isDeleting: false, // 新增的删除状态
      // 新增删除交互
      deleteInteraction: null,
      drawnWeatherTypes: [], // 已绘制的天气类型
      startMonth: 1,
      startDay: 1,
      startHour: 0,
      endMonth: 1,
      endDay: 1,
      endHour: 0,
      publishTimeStyle: {
        fontSize: 16,
        fontFamily: 'SimHei',
        color: '#000000'
      },
      // 叠加站点弹窗相关
      stationDialogVisible: false,
      stationList: [],
      selectedStations: [],
      stationLoading: false,
      stationLayer: null, // 站点图层

      // 站点定位相关
      stationScaleX: 12, // 经度缩放比例
      stationScaleY: 6, // 纬度缩放比例
      selectedCustomText: null, // 当前选中的自定义文本索引

      // 站点相关
      mapStations: [], // 预览图上的站点数组
      selectedStationIds: [], // 记录已勾选的站点ID
      selectedStation: null, // 当前选中的站点索引

      // 新增站点图层相关属性
      mapStationLayer: null, // 地图上的站点图层
      mapStationSource: null, // 站点数据源
      selectedMapStations: [], // 在地图上已选择的站点
      stationFeatures: {}, // 站点特征对象缓存
      stationsNeedUpdate: false, // 标记站点是否需要更新
      countyOptions: [
        { value: 'all', label: '全部' }
      ],
      stationSearchValue: '',
      stationFilterValue: 'all',
      filteredStationList: []
    }
  },
  watch: {
    showPreview(newVal) {
      if (newVal && this.generatedImageData) {
        // 延迟200ms更新预览图片，确保DOM已经渲染完成
        setTimeout(() => {
          this.updatePreviewImage()
          // 只在初始化时调用强制同步，避免覆盖用户输入
          this.forceUpdateInputValues()
        }, 200)
      }
    },
    // 监听标题样式的变化，立即更新预览图
    'titleStyle': {
      handler() {
        if (this.showPreview) this.updatePreviewImageImmediate()
      },
      deep: true
    },
    // 监听副标题样式的变化，立即更新预览图
    'subtitleStyle': {
      handler() {
        if (this.showPreview) this.updatePreviewImageImmediate()
      },
      deep: true
    },
    // 监听落款样式的变化，立即更新预览图
    'signatureStyle': {
      handler() {
        if (this.showPreview) this.updatePreviewImageImmediate()
      },
      deep: true
    },
    previewTitle(newVal, oldVal) {
      console.log('previewTitle 数据变化:', { from: oldVal, to: newVal })
      // 主标题变化时更新预览
      if (this.showPreview) {
        this.updatePreviewImageImmediate()
      }
    },
    previewSubtitle(newVal, oldVal) {
      // 副标题变化时更新预览
      if (this.showPreview) {
        this.updatePreviewImageImmediate()
      }
    },
    signatureText(newVal, oldVal) {
      // 落款变化时更新预览
      if (this.showPreview) {
        this.updatePreviewImageImmediate()
      }
    },
    publishTime(newVal, oldVal) {
      // 发布时间变化时更新预览
      if (this.showPreview) {
        this.updatePreviewImageImmediate()
      }
    }
  },
  mounted() {
    this.init()

    // 监视预览画布尺寸变化
    window.addEventListener('resize', this.updatePreviewImageImmediate)

    // 组件挂载后强制刷新标题和副标题
    this.$nextTick(() => {
      this.$forceUpdate()
    })

    // 在组件初始化时就获取站点数据，避免多次请求
    this.fetchStationData()
  },
  beforeDestroy() {
    // 清理地图及其交互
    if (this.map) {
      this.map.setTarget(null)
    }

    // 清理绘制交互
    if (this.drawInteraction) {
      this.map.removeInteraction(this.drawInteraction)
    }

    // 清理修改交互
    if (this.modifyInteraction) {
      this.map.removeInteraction(this.modifyInteraction)
    }

    // 清理捕捉交互
    if (this.snapInteraction) {
      this.map.removeInteraction(this.snapInteraction)
    }

    // 清理删除交互
    if (this.deleteInteraction) {
      unByKey(this.deleteInteraction)
    }
  },
  methods: {
    init() {
      const _this = this
      _this.map = new Map({
        target: 'map',
        layers: [
          new TileLayer({
            opacity: 1,
            className: 'baseMap',
            source: new XYZ({
              url: baseMapDict.nw.vec_w,
              projection: 'EPSG:3857'
            })
          }),
          new TileLayer({
            opacity: 1,
            className: 'annotation',
            source: new XYZ({
              url: annotationDict.nw.vec_w,
              projection: 'EPSG:3857'
            })
          })
        ],
        view: new View({
          center: fromLonLat([106.8, 41.24]),
          zoom: 8,
          projection: 'EPSG:3857'
        }),
        // 禁用双击放大
        interactions: defaults({
          doubleClickZoom: false
        })
      })

      // 获取城市信息
      this.getCityInfo()

      // 初始化边界
      this.initBoundaryVector()

      // 初始化天气等级
      this.initWeatherLevels()

      // 初始化天气图层
      this.initWeatherLayer()

      // 初始化站点图层
      this.initStationLayer()

      // 初始化日期时间输入
      const now = new Date()
      this.startMonth = now.getMonth() + 1
      this.startDay = now.getDate()
      this.startHour = now.getHours()

      // 计算24小时后的时间
      const later = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      this.endMonth = later.getMonth() + 1
      this.endDay = later.getDate()
      this.endHour = later.getHours()

      // 处理天气类型变化，初始化天气等级
      this.initWeatherLevels()

      // 初始化所有预览相关字段 - 放在最后确保所有数据都准备好了
      this.initializePreviewData()

      this.pubdate = new Date()

      // 添加站点点击事件
      this.map.on('click', (evt) => {
        const feature = this.map.forEachFeatureAtPixel(evt.pixel, (feature) => {
          // 返回站点要素
          if (feature.get('name')) {
            return feature
          }
        })

        if (feature) {
          // 点击到了站点
          const stationName = feature.get('name')
          const stationId = feature.get('id')

          // 找到对应的站点索引并选中它
          const stationIndex = this.mapStations.findIndex(s => s.id === stationId)
          if (stationIndex !== -1) {
            this.selectedStation = stationIndex
          }
        }
      })
    },

    // 新增方法：获取站点数据并缓存
    fetchStationData() {
      this.stationLoading = true

      stationByAreaAndStalvel()
        .then(res => {
          if (res.code === 200) {
            // 处理站点数据
            if (res.data.stations && res.data.stations.length > 0) {
              this.stationList = res.data.stations
              this.filteredStationList = [...this.stationList]

              // 收集所有县区并去重生成筛选选项
              this.generateCountyOptions()
            } else {
              this.stationList = []
              this.filteredStationList = []
              console.warn('获取站点数据为空')
            }
          } else {
            this.$message.error(res.msg || '获取站点数据失败')
            this.stationList = []
            this.filteredStationList = []
          }
          this.stationLoading = false
        })
        .catch(err => {
          console.error('获取站点数据错误:', err)
          this.$message.error('获取站点数据发生错误')
          this.stationList = []
          this.filteredStationList = []
          this.stationLoading = false
        })
    },

    getCityInfo() {
      const _this = this
      cityInfo()
        .then(res => {
          if (res.code === 200) {
            // 获取城市坐标
            _this.cityName = res.data.cityInfo.CityName

            // 设置地图中心
            const coordinates = res.data.cityInfo.LatLon.split(',')
            const lon = parseFloat(coordinates[0])
            const lat = parseFloat(coordinates[1])

            // 向左移动20%
            const offsetLon = lon + (lon * 0.015)

            _this.map
              .getView()
              .setCenter(
                fromLonLat([offsetLon, lat])
              )

            // 强制更新DOM
            _this.$nextTick(() => {
              _this.$forceUpdate()
            })
          }
        })
        .catch(err => { })
    },

    initBoundaryVector() {
      const _this = this
      const geojson = 'static/json/' + getCookie('areaCode') + '.json'

      // 添加内部旗县边界
      const source0 = new SourceVector({
        projection: 'EPSG:4326',
        url: 'static/json/' + getCookie('areaCode') + '1.json',
        format: new GeoJSON()
      })
      _this.boundaryVector0 = new LayerVector({
        source: source0,
        style: new Style({
          stroke: new Stroke({
            color: '#300dd7',
            width: 2.0
          })
        }),
        // 设置内部边界线的z-index，确保在天气图层上方
        zIndex: 10
      })
      _this.map.addLayer(_this.boundaryVector0)

      // 添加主边界
      const source1 = new SourceVector({
        projection: 'EPSG:4326',
        url: geojson,
        format: new GeoJSON()
      })
      _this.boundaryVector1 = new LayerVector({
        source: source1,
        style: new Style({
          stroke: new Stroke({
            color: '#300dd7',
            width: 2.5
          })
        }),
        // 设置主边界线的z-index，确保在天气图层上方
        zIndex: 11
      })
      _this.map.addLayer(_this.boundaryVector1)

      // 添加警戒区和监视区
      fetch(geojson)
        .then(function(response) {
          return response.json()
        })
        .then(function(json) {
          const format = new GeoJSON()
          const features = format.readFeatures(json)
          const feature = features[0]
          const turfLine = format.writeFeatureObject(feature)
        })
    },

    // 初始化天气图层
    initWeatherLayer() {
      this.weatherSource = new SourceVector()
      this.weatherLayer = new LayerVector({
        source: this.weatherSource,
        style: (feature) => {
          const weatherType = feature.get('weatherType')
          const weatherLevel = feature.get('weatherLevel')

          // 查找对应的天气等级颜色
          let color = '#FF0000' // 默认颜色
          const levelConfig = this.weatherLevelConfig[weatherType]
          if (levelConfig) {
            const level = levelConfig.find(l => l.value === weatherLevel)
            if (level) {
              // 使用完全不透明的颜色，去除透明度
              color = level.color
            }
          }

          // 雨夹雪使用特殊样式
          if (weatherType === 'sleet') {
            // 创建雨夹雪的特殊图案（斜线填充）
            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')
            canvas.width = 10
            canvas.height = 10

            // 绘制背景
            context.fillStyle = color
            context.fillRect(0, 0, 10, 10)

            // 绘制斜线
            context.strokeStyle = 'white'
            context.lineWidth = 1
            context.beginPath()
            context.moveTo(0, 10)
            context.lineTo(10, 0)
            context.stroke()

            // 使用图案填充
            return new Style({
              fill: new Fill({
                color: context.createPattern(canvas, 'repeat')
              })
            })
          } else {
            // 其他天气类型使用普通填充
            return new Style({
              fill: new Fill({
                color: color
              })
            })
          }
        },
        // 设置图层渲染顺序
        renderOrder: (feature1, feature2) => {
          const level1 = this.getWeatherLevelOrder(feature1.get('weatherLevel'))
          const level2 = this.getWeatherLevelOrder(feature2.get('weatherLevel'))
          return level2 - level1 // 强度大的在后渲染（显示在上层）
        },
        // 设置天气图层的z-index，确保在边界线下方
        zIndex: 1
      })

      this.map.addLayer(this.weatherLayer)
    },

    // 获取天气等级的顺序（强度从弱到强）
    getWeatherLevelOrder(level) {
      const levelConfig = this.weatherLevelConfig[this.weatherType]
      if (!levelConfig) return 0

      // 直接返回天气等级的数值
      const levelObj = levelConfig.find(l => l.value === level)
      return levelObj ? levelObj.value : 0
    },

    // 开始绘制
    startDrawing() {
      if (this.isDrawing) {
        this.stopDrawing()
        return
      }

      this.isDrawing = true

      // 创建绘制交互
      this.drawInteraction = new Draw({
        source: this.weatherSource,
        type: 'Polygon',

        // 使用曲线绘制
        geometryFunction: (coordinates, geometry) => {
          if (!geometry) {
            geometry = new Polygon([coordinates[0]])
          } else {
            const coords = coordinates[0]
            // 如果有足够的点，创建平滑曲线
            if (coords.length > 2) {
              try {
                // 创建平滑曲线点
                const smoothCoords = this.createSmoothCurve(coords)
                if (smoothCoords && smoothCoords.length > 2) {
                  // 确保首尾相连
                  if (smoothCoords[0][0] !== smoothCoords[smoothCoords.length - 1][0] ||
                                        smoothCoords[0][1] !== smoothCoords[smoothCoords.length - 1][1]) {
                    smoothCoords.push(smoothCoords[0])
                  }
                  geometry.setCoordinates([smoothCoords])
                } else {
                  geometry.setCoordinates([coords])
                }
              } catch (error) {
                console.error('Error creating smooth curve:', error)
                geometry.setCoordinates([coords])
              }
            } else {
              geometry.setCoordinates([coords])
            }
          }
          return geometry
        },
        // 设置绘制样式
        style: (feature) => {
          const weatherType = this.weatherType
          const weatherLevel = this.weatherLevel

          // 查找对应的天气等级颜色
          let color = '#FF0000' // 默认颜色
          const levelConfig = this.weatherLevelConfig[weatherType]
          if (levelConfig) {
            const level = levelConfig.find(l => l.value === weatherLevel)
            if (level) {
              // 使用完全不透明的颜色，去除透明度
              color = level.color
            }
          }

          // 雨夹雪使用特殊样式
          if (weatherType === 'sleet') {
            // 创建雨夹雪的特殊图案（斜线填充）
            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')
            canvas.width = 10
            canvas.height = 10

            // 绘制背景
            context.fillStyle = color
            context.fillRect(0, 0, 10, 10)

            // 绘制斜线
            context.strokeStyle = 'white'
            context.lineWidth = 1
            context.beginPath()
            context.moveTo(0, 10)
            context.lineTo(10, 0)
            context.stroke()

            // 使用图案填充
            return new Style({
              fill: new Fill({
                color: context.createPattern(canvas, 'repeat')
              })
            })
          } else {
            // 其他天气类型使用普通填充
            return new Style({
              fill: new Fill({
                color: color
              })
            })
          }
        },
        // 设置最大点数限制，防止性能问题
        maxPoints: 100,
        // 设置点击容差
        clickTolerance: 6
      })

      // 添加绘制完成监听
      this.drawListener = this.drawInteraction.on('drawend', (event) => {
        const feature = event.feature
        const geometry = feature.getGeometry()
        const coordinates = geometry.getCoordinates()[0]

        // 确保首尾相连
        if (coordinates[0][0] !== coordinates[coordinates.length - 1][0] ||
                    coordinates[0][1] !== coordinates[coordinates.length - 1][1]) {
          coordinates.push(coordinates[0])
          geometry.setCoordinates([coordinates])
        }

        // 设置特性属性
        feature.set('weatherType', this.weatherType)
        feature.set('weatherLevel', this.weatherLevel)

        // 获取当前选中的天气等级颜色
        const levelConfig = this.weatherLevelConfig[this.weatherType]
        const level = levelConfig.find(l => l.value === this.weatherLevel)
        if (level) {
          feature.set('color', level.color)
        }

        // 处理重叠区域（仅在启用时执行，以提高性能）
        if (this.enableOverlapProcessing) {
          this.handleOverlappingAreas(feature)
        }

        // 更新已绘制的天气类型
        this.drawnWeatherTypes = this.getDrawnWeatherTypes()

        // 停止绘制
        this.stopDrawing()

        // 添加修改交互
        this.addModifyInteraction()
      })

      this.map.addInteraction(this.drawInteraction)
    },

    // 创建平滑曲线
    createSmoothCurve(points) {
      // 如果点数太少，直接返回原始点
      if (points.length < 3) {
        return points
      }

      const smoothPoints = []
      const numOfSegments = 8 // 减少分段数以提高性能，原来是32

      // 对每一段进行插值
      for (let i = 0; i < points.length; i++) {
        const currentPoint = points[i]
        const nextPoint = points[(i + 1) % points.length]
        const prevPoint = points[(i - 1 + points.length) % points.length]
        const nextNextPoint = points[(i + 2) % points.length]

        // 添加当前点
        smoothPoints.push(currentPoint)

        // 计算当前点到前后点的距离
        const distToPrev = Math.sqrt(
          Math.pow(currentPoint[0] - prevPoint[0], 2) +
                    Math.pow(currentPoint[1] - prevPoint[1], 2)
        )
        const distToNext = Math.sqrt(
          Math.pow(nextPoint[0] - currentPoint[0], 2) +
                    Math.pow(nextPoint[1] - currentPoint[1], 2)
        )

        // 计算切线方向
        const dx1 = nextPoint[0] - prevPoint[0]
        const dy1 = nextPoint[1] - prevPoint[1]
        const length = Math.sqrt(dx1 * dx1 + dy1 * dy1)

        // 计算下一个切线方向
        const dx2 = nextNextPoint[0] - currentPoint[0]
        const dy2 = nextNextPoint[1] - currentPoint[1]
        const length2 = Math.sqrt(dx2 * dx2 + dy2 * dy2)

        // 根据距离计算控制点的长度（使用较小的比例以确保曲线不会偏离太多）
        const controlLength = Math.min(distToPrev, distToNext) * 0.3

        // 计算控制点（考虑前后点的方向）
        const controlPoint1 = [
          currentPoint[0] + (dx1 / length) * controlLength,
          currentPoint[1] + (dy1 / length) * controlLength
        ]
        const controlPoint2 = [
          nextPoint[0] - (dx2 / length2) * controlLength,
          nextPoint[1] - (dy2 / length2) * controlLength
        ]

        // 使用三次贝塞尔曲线进行插值
        for (let t = 0; t <= numOfSegments; t++) {
          const t1 = t / numOfSegments
          const t2 = t1 * t1
          const t3 = t2 * t1

          // 三次贝塞尔曲线公式
          const x = (1 - t1) * (1 - t1) * (1 - t1) * currentPoint[0] +
                             3 * (1 - t1) * (1 - t1) * t1 * controlPoint1[0] +
                             3 * (1 - t1) * t2 * controlPoint2[0] +
                             t3 * nextPoint[0]

          const y = (1 - t1) * (1 - t1) * (1 - t1) * currentPoint[1] +
                             3 * (1 - t1) * (1 - t1) * t1 * controlPoint1[1] +
                             3 * (1 - t1) * t2 * controlPoint2[1] +
                             t3 * nextPoint[1]

          // 确保点在有效范围内
          if (this.isValidCoordinate([x, y])) {
            smoothPoints.push([x, y])
          }
        }
      }

      return smoothPoints
    },

    // 检查坐标是否有效（简化版本以提高性能）
    isValidCoordinate(coord) {
      // 简化坐标验证，只检查基本有效性
      return !isNaN(coord[0]) && !isNaN(coord[1]) &&
                   Math.abs(coord[0]) < 20037508.34 &&
                   Math.abs(coord[1]) < 20037508.34
    },

    // 停止绘制
    stopDrawing() {
      this.isDrawing = false

      if (this.drawInteraction) {
        this.map.removeInteraction(this.drawInteraction)
        this.drawInteraction = null
      }

      if (this.drawListener) {
        unByKey(this.drawListener)
        this.drawListener = null
      }
    },

    // 添加修改交互
    addModifyInteraction() {
      // 移除之前的修改交互
      if (this.modifyInteraction) {
        this.map.removeInteraction(this.modifyInteraction)
      }

      // 创建新的修改交互
      this.modifyInteraction = new Modify({
        source: this.weatherSource
      })

      // 添加捕捉交互
      this.snapInteraction = new Snap({
        source: this.weatherSource
      })

      this.map.addInteraction(this.modifyInteraction)
      this.map.addInteraction(this.snapInteraction)
    },

    // 初始化天气等级选项
    initWeatherLevels() {
      this.weatherLevels = this.weatherLevelConfig[this.weatherType]
      if (this.weatherLevels && this.weatherLevels.length > 0) {
        this.weatherLevel = this.weatherLevels[0].value
      }
    },

    // 处理天气类型变化
    handleWeatherTypeChange() {
      // 初始化天气等级选项
      this.initWeatherLevels()

      // 只在预览标题为默认值时更新，保留用户修改
      if (!this.previewTitle || this.previewTitle.includes('预报图')) {
        this.initializePreviewData()
      }

      // 强制更新DOM和视图
      this.$nextTick(() => {
        this.$forceUpdate()
        if (this.showPreview) {
          this.updatePreviewImage()
        }
      })
    },

    // 选择天气等级
    selectWeatherLevel(value) {
      this.weatherLevel = value
    },

    // 打开颜色选择器
    openColorPicker(item) {
      const colorPicker = this.$refs[`colorPicker-${item.value}`][0]
      colorPicker.click()
    },

    // 更新颜色
    updateColor(item, event) {
      const newColor = event.target.value
      // 找到对应的天气等级并更新颜色
      const levelIndex = this.weatherLevels.findIndex(level => level.value === item.value)
      if (levelIndex !== -1) {
        this.weatherLevels[levelIndex].color = newColor
        // 同时更新配置中的颜色
        const configIndex = this.weatherLevelConfig[this.weatherType].findIndex(level => level.value === item.value)
        if (configIndex !== -1) {
          this.weatherLevelConfig[this.weatherType][configIndex].color = newColor
        }

        // 更新已绘制的区域颜色
        this.updateDrawnFeatures()
      }
    },

    // 更新已绘制的区域颜色
    updateDrawnFeatures() {
      const features = this.weatherSource.getFeatures()
      features.forEach(feature => {
        const featureType = feature.get('weatherType')
        const featureLevel = feature.get('weatherLevel')

        if (featureType === this.weatherType && featureLevel === this.weatherLevel) {
          // 获取当前选中的天气等级颜色
          const levelConfig = this.weatherLevelConfig[this.weatherType]
          const level = levelConfig.find(l => l.value === this.weatherLevel)
          if (level) {
            feature.set('color', level.color)
          }
        }
      })

      // 触发重绘
      this.weatherLayer.changed()
    },

    // 生成图片
    generateImage() {
      // 创建一个临时 canvas
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      // 获取边界特征
      const boundaryFeature = this.boundaryVector1.getSource().getFeatures()[0]
      if (!boundaryFeature) {
        this.$message.error('未找到边界数据')
        return
      }

      // 获取已绘制的天气类型
      this.drawnWeatherTypes = this.getDrawnWeatherTypes()

      // 获取边界几何
      const boundaryGeometry = boundaryFeature.getGeometry()
      const extent = boundaryGeometry.getExtent() // 获取边界的范围

      // 计算像素坐标
      const topLeft = this.map.getPixelFromCoordinate([extent[0], extent[3]])
      const bottomRight = this.map.getPixelFromCoordinate([extent[2], extent[1]])

      // 计算边界的像素宽度和高度
      const pixelWidth = Math.abs(bottomRight[0] - topLeft[0])
      const pixelHeight = Math.abs(bottomRight[1] - topLeft[1])

      // 添加更多内边距，确保有足够空间显示标题等内容
      const paddingTop = 100 // 顶部增加更多空间
      const paddingBottom = 50
      const paddingLeft = 10
      const paddingRight = 10

      canvas.width = pixelWidth + paddingLeft + paddingRight
      canvas.height = pixelHeight + paddingTop + paddingBottom

      // 创建一个临时地图容器
      const tempMapDiv = document.createElement('div')
      tempMapDiv.style.width = canvas.width + 'px'
      tempMapDiv.style.height = canvas.height + 'px'
      tempMapDiv.style.position = 'absolute'
      tempMapDiv.style.left = '-9999px'
      tempMapDiv.style.backgroundColor = '#FFFFFF'
      document.body.appendChild(tempMapDiv)

      // 准备临时地图图层列表，包括站点图层
      const mapLayers = [
        this.boundaryVector1,
        this.boundaryVector0,
        this.weatherLayer
      ]

      // 如果有站点图层，也添加到临时地图中
      if (this.mapStationLayer) {
        mapLayers.push(this.mapStationLayer)
      }

      // 创建临时地图
      const tempMap = new Map({
        target: tempMapDiv,
        view: new View({
          center: this.map.getView().getCenter(),
          zoom: this.map.getView().getZoom(),
          projection: 'EPSG:3857'
        }),
        layers: mapLayers
      })

      // 视图缩放到边界范围
      tempMap.getView().fit(extent, {
        size: [canvas.width, canvas.height],
        padding: [paddingTop, paddingRight, paddingBottom, paddingLeft]
      })

      // 保存临时地图的尺寸和视图状态，用于后续计算站点位置
      const tempMapSize = tempMap.getSize()
      const tempViewCenter = tempMap.getView().getCenter()
      const tempViewResolution = tempMap.getView().getResolution()

      // 等待图层加载完成
      setTimeout(() => {
        const mapCanvas = tempMapDiv.querySelector('canvas')
        if (mapCanvas) {
          // 绘制白色背景
          ctx.fillStyle = '#FFFFFF'
          ctx.fillRect(0, 0, canvas.width, canvas.height)

          // 获取边界特征
          const boundaryCoordinates = boundaryGeometry.getCoordinates()[0]

          // 将坐标从地图坐标转换为屏幕坐标
          const screenCoords = boundaryCoordinates.map(coord => {
            const pixel = tempMap.getPixelFromCoordinate(coord)
            return [pixel[0], pixel[1]]
          })

          // 创建裁剪路径
          ctx.beginPath()
          ctx.moveTo(screenCoords[0][0], screenCoords[0][1])
          for (let i = 1; i < screenCoords.length; i++) {
            ctx.lineTo(screenCoords[i][0], screenCoords[i][1])
          }
          ctx.closePath()

          // 保存当前状态
          ctx.save()

          // 设置裁剪区域
          ctx.clip()

          // 绘制地图（包括边界和天气图层）
          ctx.drawImage(mapCanvas, 0, 0, canvas.width, canvas.height)

          // 恢复状态
          ctx.restore()

          // 重新绘制边界线
          ctx.beginPath()
          ctx.moveTo(screenCoords[0][0], screenCoords[0][1])
          for (let i = 1; i < screenCoords.length; i++) {
            ctx.lineTo(screenCoords[i][0], screenCoords[i][1])
          }
          ctx.closePath()
          ctx.strokeStyle = '#300dd7'
          ctx.lineWidth = 2.5
          ctx.stroke()

          // 保存生成的图片数据
          this.generatedImageData = canvas.toDataURL('image/png')

          // 只在预览数据为空时初始化，避免覆盖用户输入
          if (!this.previewTitle || this.previewTitle === '内蒙古降水预报图') {
            this.initializePreviewData()
          }

          // 设置首次加载标志为true
          this.firstLoad = true

          // 更新站点位置 - 使用临时地图的视图状态
          if (this.mapStations.length > 0 && this.mapStationSource) {
            // 遍历所有站点，更新它们的位置
            this.mapStations.forEach(station => {
              const feature = this.stationFeatures[station.id]
              if (feature) {
                const coords = feature.getGeometry().getCoordinates()
                // 使用临时地图的变换，确保站点位置与地图匹配
                const pixelCoords = tempMap.getPixelFromCoordinate(coords)

                // 直接使用临时地图的像素坐标设置站点位置
                station.position = {
                  x: pixelCoords[0],
                  y: pixelCoords[1]
                }
              }
            })

            // 记录地图的边界信息，用于updatePreviewImage方法
            this.tempMapInfo = {
              width: canvas.width,
              height: canvas.height,
              center: tempViewCenter,
              resolution: tempViewResolution
            }
          }

          // 显示预览对话框
          this.showPreview = true

          // 在预览对话框显示后的处理
          this.$nextTick(() => {
            this.addCompassToCanvas()
            this.updatePreviewImage()

            // 移除强制同步调用，避免覆盖用户输入
          })

          // 清理临时元素
          document.body.removeChild(tempMapDiv)
          tempMap.setTarget(null)
        }
      }, 500)
    },

    // 更新预览图片
    updatePreviewImage() {
      if (!this.generatedImageData) return

      const canvas = this.$refs.previewCanvas
      if (!canvas) {
        console.error('找不到预览画布元素')
        return
      }

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        console.error('无法获取画布上下文')
        return
      }

      // 直接调用更新方法，不使用防抖延迟
      this.doUpdatePreviewImage()

      // 移除强制字符串转换，避免标题显示异常
      // 只在必要时强制更新视图
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },

    // 更新标题和副标题的样式确保居中显示
    updateCenteredTitles() {
      if (this.$refs.previewTitle) {
        this.$refs.previewTitle.style.transform = 'translateX(-50%)'
        this.$refs.previewTitle.style.textAlign = 'center'
      }
      if (this.$refs.previewSubtitle) {
        this.$refs.previewSubtitle.style.transform = 'translateX(-50%)'
        this.$refs.previewSubtitle.style.textAlign = 'center'
      }
      if (this.$refs.previewSignature) {
        this.$refs.previewSignature.style.transform = 'translateX(-50%)'
        this.$refs.previewSignature.style.textAlign = 'center'
      }
      if (this.$refs.approvalNumber) {
        this.$refs.approvalNumber.style.transform = 'translateX(-50%)'
        this.$refs.approvalNumber.style.textAlign = 'center'
      }
      if (this.$refs.publishTime) {
        this.$refs.publishTime.style.transform = 'translateX(-50%)'
        this.$refs.publishTime.style.textAlign = 'center'
      }
    },

    // 处理重叠区域
    handleOverlappingAreas(newFeature) {
      const newGeometry = newFeature.getGeometry()
      const newLevel = this.getWeatherLevelOrder(newFeature.get('weatherLevel'))
      const features = this.weatherSource.getFeatures()

      // 遍历所有已存在的特征
      features.forEach(feature => {
        if (feature === newFeature) return

        const existingGeometry = feature.getGeometry()
        const existingLevel = this.getWeatherLevelOrder(feature.get('weatherLevel'))

        try {
          // 计算重叠区域
          const intersection = turf.intersect(
            turf.polygon(newGeometry.getCoordinates()),
            turf.polygon(existingGeometry.getCoordinates())
          )

          if (intersection) {
            if (newLevel >= existingLevel) {
              // 如果新特征的等级更高或相等，从现有特征中减去重叠区域
              const difference = turf.difference(
                turf.polygon(existingGeometry.getCoordinates()),
                intersection
              )

              if (difference) {
                // 更新现有特征的几何形状
                existingGeometry.setCoordinates(difference.geometry.coordinates)
              } else {
                // 如果完全重叠，删除该特征
                this.weatherSource.removeFeature(feature)
              }
            } else {
              // 如果新特征的等级更低，从新特征中减去重叠区域
              const difference = turf.difference(
                turf.polygon(newGeometry.getCoordinates()),
                intersection
              )

              if (difference) {
                // 更新新特征的几何形状
                newGeometry.setCoordinates(difference.geometry.coordinates)
              } else {
                // 如果完全重叠，删除新特征
                this.weatherSource.removeFeature(newFeature)
                return
              }
            }
          }
        } catch (error) {
          console.error('Error handling overlapping areas:', error)
        }
      })
    },

    // 开始拖拽
    startDrag(event, type, index) {
      event.preventDefault() // 防止文本选择
      this.isDragging = true
      this.dragging = true // 保持兼容性
      this.dragType = type
      this.dragIndex = index // 保存拖拽的索引
      this.dragStartX = event.clientX
      this.dragStartY = event.clientY

      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', this.handleDrag)
      document.addEventListener('mouseup', this.stopDrag)

      if (type === 'customText') {
        this.selectedCustomText = index
        // 当选择自定义文本时，取消站点选择
        this.selectedStation = null
      } else if (type === 'station') {
        this.selectStation(index)
        // 记录站点的初始位置
        this.dragStartElementX = this.mapStations[index].position.x
        this.dragStartElementY = this.mapStations[index].position.y
      }
    },

    // 处理拖拽
    handleDrag(event) {
      if (!this.dragging && !this.isDragging) return

      const deltaX = event.clientX - this.dragStartX
      const deltaY = event.clientY - this.dragStartY

      switch (this.dragType) {
        case 'title':
          this.titlePosition.x += deltaX
          this.titlePosition.y += deltaY
          break
        case 'subtitle':
          this.subtitlePosition.x += deltaX
          this.subtitlePosition.y += deltaY
          break
        case 'legend':
          this.legendPosition.x += deltaX
          this.legendPosition.y += deltaY
          break
        case 'signature':
          this.signaturePosition.x += deltaX
          this.signaturePosition.y += deltaY
          break
        case 'approvalNumber':
          this.mapApprovalNumberPosition.x += deltaX
          this.mapApprovalNumberPosition.y += deltaY
          break
        case 'publishTime':
          this.publishTimePosition.x += deltaX
          this.publishTimePosition.y += deltaY
          break
        case 'customText':
          // 使用dragIndex确定是哪个自定义文字
          if (this.dragIndex !== undefined && this.customTexts[this.dragIndex]) {
            this.customTexts[this.dragIndex].position.x += deltaX
            this.customTexts[this.dragIndex].position.y += deltaY
          }
          break
        case 'image':
          // 拖拽图片
          if (this.dragIndex !== undefined && this.canvasImages[this.dragIndex]) {
            this.canvasImages[this.dragIndex].position.x += deltaX
            this.canvasImages[this.dragIndex].position.y += deltaY
          }
          break
        case 'station':
          // 使用dragIndex确定是哪个站点
          if (this.dragIndex !== undefined && this.mapStations[this.dragIndex]) {
            // 如果使用了初始位置 + 总偏移量的方式
            if (this.dragStartElementX !== undefined && this.dragStartElementY !== undefined) {
              this.mapStations[this.dragIndex].position.x = this.dragStartElementX + deltaX
              this.mapStations[this.dragIndex].position.y = this.dragStartElementY + deltaY
            } else {
              // 兼容原有方式
              this.mapStations[this.dragIndex].position.x += deltaX
              this.mapStations[this.dragIndex].position.y += deltaY
            }

            // 标记站点需要更新
            this.stationsNeedUpdate = true
          }
          break
      }

      this.dragStartX = event.clientX
      this.dragStartY = event.clientY

      // 更新预览图片
      this.$nextTick(() => {
        this.updatePreviewImage()
      })
    },

    // 停止拖拽
    stopDrag() {
      this.dragging = false
      this.dragType = null
      this.dragIndex = undefined // 清除拖拽索引

      // 移除全局鼠标事件监听
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },

    // 关闭预览
    closePreview() {
      this.showPreview = false
      this.generatedImageData = null
      this.firstLoad = true // 重置firstLoad，以便下次打开预览时能正确设置初始位置

      // 重置自定义文字位置标志，让下次预览重新计算位置
      this.customTexts.forEach(textItem => {
        textItem.positioned = false
      })

      // 清空画布图片，确保下次生成时重新添加指南针
      this.canvasImages = []

      // 清空已选图片
      this.selectedImage = null
      this.selectedCanvasImage = null
    },

    // 下载图片
    downloadImage() {
      if (!this.showPreview) return

      // 获取预览画布
      const previewCanvas = this.$refs.previewCanvas
      if (!previewCanvas) {
        console.error('预览画布未找到')
        return
      }

      // 创建一个新画布，与预览画布大小相同
      const exportCanvas = document.createElement('canvas')
      const exportCtx = exportCanvas.getContext('2d')
      exportCanvas.width = previewCanvas.width
      exportCanvas.height = previewCanvas.height

      // 先绘制白色背景
      exportCtx.fillStyle = '#FFFFFF'
      exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height)

      // 复制预览画布内容
      exportCtx.drawImage(previewCanvas, 0, 0)

      // 获取预览容器中的所有元素（标题、副标题、图例等）
      const previewContainer = this.$refs.previewContainer

      // 创建临时的HTML2Canvas对象来渲染DOM元素
      try {
        // 获取图片位置和大小信息，用于定位元素
        const rect = previewCanvas.getBoundingClientRect()

        // 手动渲染标题
        if (this.previewTitle) {
          exportCtx.font = `${this.titleStyle.fontWeight} ${this.titleStyle.fontSize}px ${this.formatFontFamily(this.titleStyle.fontFamily)}`
          exportCtx.fillStyle = this.titleStyle.color
          exportCtx.textAlign = 'center'
          exportCtx.fillText(this.previewTitle, this.titlePosition.x, this.titlePosition.y)
        }

        // 手动渲染副标题
        if (this.previewSubtitle) {
          exportCtx.font = `${this.subtitleStyle.fontSize}px ${this.formatFontFamily(this.subtitleStyle.fontFamily)}`
          exportCtx.fillStyle = this.subtitleStyle.color
          exportCtx.textAlign = 'center'
          exportCtx.fillText(this.previewSubtitle, this.subtitlePosition.x, this.subtitlePosition.y)
        }

        // 绘制图例
        if (this.previewShowLegend) {
          // 判断是否有多种天气类型
          const hasMultiTypes = this.drawnWeatherTypes.length > 1

          // 设置字体大小 - 多种类型时缩小30%
          const titleFontSize = hasMultiTypes ? this.legendStyle.titleFontSize * 0.7 : this.legendStyle.titleFontSize
          const itemFontSize = hasMultiTypes ? this.legendStyle.itemFontSize * 0.7 : this.legendStyle.itemFontSize

          // 计算图例文字的最大宽度 (每种类型)
          const typeMaxWidths = []
          this.drawnWeatherTypes.forEach((type, index) => {
            exportCtx.font = `${itemFontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
            let maxWidth = 0
            this.weatherLevelConfig[type].forEach(level => {
              const textWidth = exportCtx.measureText(level.label).width
              maxWidth = Math.max(maxWidth, textWidth)
            })
            typeMaxWidths.push(maxWidth)
          })

          // 颜色块尺寸
          const colorBlockWidth = hasMultiTypes ? 15 : 15
          const colorBlockMargin = 10
          const padding = 20

          // 计算每种类型所需的宽度
          const typeWidths = typeMaxWidths.map(width =>
            width + colorBlockWidth + colorBlockMargin + (hasMultiTypes ? 10 : 20))

          // 根据排列方式计算总宽度
          let legendWidth = 0
          if (hasMultiTypes) {
            // 水平排列时宽度是所有类型宽度之和加上间隔
            legendWidth = typeWidths.reduce((sum, width) => sum + width, 0) + padding * 2
          } else {
            // 垂直排列时宽度是最宽类型的宽度
            legendWidth = Math.max(...typeWidths) + padding * 2
          }

          // 计算每个类型的高度
          const typeTitleHeight = 20
          const itemHeight = 25
          const typeMargin = 15

          const typeHeights = this.drawnWeatherTypes.map(type =>
            typeTitleHeight + (this.weatherLevelConfig[type].length * itemHeight))

          // 计算图例总高度
          let legendHeight = 0
          const titleHeight = 30

          if (hasMultiTypes) {
            // 水平排列时高度是最高类型的高度加上标题
            legendHeight = Math.max(...typeHeights) + titleHeight + padding * 2
          } else {
            // 垂直排列时高度是所有类型高度之和加上间距和标题
            legendHeight = typeHeights.reduce((sum, height) => sum + height, 0) +
                                    ((typeHeights.length - 1) * typeMargin) + titleHeight + padding * 2
          }

          // 绘制图例背景
          exportCtx.fillStyle = this.legendStyle.backgroundColor
          exportCtx.fillRect(this.legendPosition.x, this.legendPosition.y, legendWidth, legendHeight)

          // 绘制图例标题
          exportCtx.font = `bold ${titleFontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
          exportCtx.fillStyle = this.legendStyle.color
          exportCtx.textAlign = 'center'
          exportCtx.fillText('图例', this.legendPosition.x + legendWidth / 2, this.legendPosition.y + titleHeight)

          // 绘制图例项
          exportCtx.textAlign = 'left'

          if (hasMultiTypes) {
            // 水平排列天气类型
            let currentX = this.legendPosition.x + padding
            const startY = this.legendPosition.y + titleHeight + padding

            this.drawnWeatherTypes.forEach((type, typeIndex) => {
              // 天气类型标题
              exportCtx.font = `bold ${itemFontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
              exportCtx.fillStyle = this.legendStyle.color
              exportCtx.fillText(this.getWeatherTypeName(type), currentX, startY)

              // 绘制该类型的所有等级
              let itemY = startY + typeTitleHeight
              this.weatherLevelConfig[type].forEach((level, levelIndex) => {
                // 绘制颜色块
                exportCtx.fillStyle = level.color
                exportCtx.fillRect(currentX, itemY, colorBlockWidth, colorBlockWidth)

                // 为雨夹雪添加斜线纹理
                if (type === 'sleet') {
                  exportCtx.strokeStyle = 'white'
                  exportCtx.lineWidth = 1

                  // 绘制斜线
                  exportCtx.beginPath()
                  for (let i = -2; i <= 2; i++) {
                    exportCtx.moveTo(currentX, itemY + i * 3 + colorBlockWidth)
                    exportCtx.lineTo(currentX + colorBlockWidth, itemY + i * 3)
                  }
                  exportCtx.stroke()
                }

                // 绘制标签
                exportCtx.fillStyle = this.legendStyle.color
                exportCtx.font = `${itemFontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
                exportCtx.fillText(level.label, currentX + colorBlockWidth + colorBlockMargin, itemY + 12)

                itemY += itemHeight
              })

              // 移动到下一个类型的水平位置
              currentX += typeWidths[typeIndex]
            })
          } else {
            // 垂直排列天气类型（原来的方式）
            let currentY = this.legendPosition.y + titleHeight + padding

            this.drawnWeatherTypes.forEach((type, typeIndex) => {
              // 天气类型标题
              exportCtx.font = `bold ${itemFontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
              exportCtx.fillText(this.getWeatherTypeName(type), this.legendPosition.x + padding, currentY)
              currentY += typeTitleHeight

              // 天气等级
              this.weatherLevelConfig[type].forEach((level, levelIndex) => {
                // 绘制颜色块
                exportCtx.fillStyle = level.color
                exportCtx.fillRect(this.legendPosition.x + padding, currentY, colorBlockWidth, colorBlockWidth)

                // 为雨夹雪添加斜线纹理
                if (type === 'sleet') {
                  exportCtx.strokeStyle = 'white'
                  exportCtx.lineWidth = 1

                  // 绘制斜线
                  exportCtx.beginPath()
                  const blockX = this.legendPosition.x + padding
                  const blockY = currentY

                  for (let i = -2; i <= 2; i++) {
                    exportCtx.moveTo(blockX, blockY + i * 3 + colorBlockWidth)
                    exportCtx.lineTo(blockX + colorBlockWidth, blockY + i * 3)
                  }
                  exportCtx.stroke()
                }

                // 绘制标签
                exportCtx.fillStyle = this.legendStyle.color
                exportCtx.font = `${itemFontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
                exportCtx.fillText(level.label, this.legendPosition.x + padding + colorBlockWidth + colorBlockMargin, currentY + 12)

                currentY += itemHeight
              })

              // 如果不是最后一个类型，添加间距
              if (typeIndex < this.drawnWeatherTypes.length - 1) {
                currentY += typeMargin
              }
            })
          }
        }

        // 添加落款（如果启用）
        if (this.previewShowSignature) {
          exportCtx.font = `${this.signatureStyle.fontSize}px ${this.formatFontFamily(this.legendStyle.fontFamily)}`
          exportCtx.textAlign = 'center' // 改为center对齐，使落款居中
          exportCtx.fillStyle = '#000000'
          exportCtx.fillText(this.signatureText, this.signaturePosition.x, this.signaturePosition.y)
        }

        // 添加审图号
        if (this.showMapApprovalNumber) {
          exportCtx.font = `${this.mapApprovalNumberStyle.fontSize}px ${this.formatFontFamily(this.mapApprovalNumberStyle.fontFamily)}`
          exportCtx.textAlign = 'center' // 设置为居中对齐
          exportCtx.fillStyle = this.mapApprovalNumberStyle.color
          exportCtx.fillText(this.mapApprovalNumber, this.mapApprovalNumberPosition.x, this.mapApprovalNumberPosition.y)
        }

        // 添加发布时间
        if (this.showPublishTime) {
          exportCtx.font = `${this.publishTimeStyle.fontSize}px ${this.formatFontFamily(this.publishTimeStyle.fontFamily)}`
          exportCtx.textAlign = 'center' // 设置为居中对齐
          exportCtx.fillStyle = this.publishTimeStyle.color
          exportCtx.fillText(this.publishTime, this.publishTimePosition.x, this.publishTimePosition.y)
        }

        // 添加自定义文字
        this.customTexts.forEach(textItem => {
          if (textItem.content.trim() !== '') {
            exportCtx.font = `${textItem.style.fontSize}px ${this.formatFontFamily(textItem.style.fontFamily)}`
            exportCtx.textAlign = 'left'
            exportCtx.fillStyle = textItem.style.color
            exportCtx.fillText(textItem.content, textItem.position.x, textItem.position.y)
          }
        })

        // 处理图片加载
        const loadImages = () => {
          return Promise.all(this.canvasImages.map(imageItem => {
            return new Promise((resolve) => {
              const img = new Image()
              img.onload = () => {
                // 保存当前上下文状态
                exportCtx.save()

                // 移动到图片中心点
                exportCtx.translate(
                  imageItem.position.x + imageItem.size / 2,
                  imageItem.position.y + (imageItem.size * img.height / img.width) / 2
                )

                // 旋转
                if (imageItem.rotation) {
                  exportCtx.rotate((imageItem.rotation * Math.PI) / 180)
                }

                // 绘制图片（相对于中心点）
                exportCtx.drawImage(
                  img,
                  -imageItem.size / 2,
                  -(imageItem.size * img.height / img.width) / 2,
                  imageItem.size,
                  imageItem.size * img.height / img.width
                )

                // 应用颜色滤镜
                if (imageItem.colorStrength > 0) {
                  // 获取绘制区域尺寸
                  const width = imageItem.size
                  const height = imageItem.size * img.height / img.width

                  // 获取绘制区域数据
                  const imageData = exportCtx.getImageData(-width / 2, -height / 2, width, height)
                  const data = imageData.data

                  // 解析色值
                  const r = parseInt(imageItem.color.slice(1, 3), 16)
                  const g = parseInt(imageItem.color.slice(3, 5), 16)
                  const b = parseInt(imageItem.color.slice(5, 7), 16)

                  // 应用颜色滤镜
                  const strength = imageItem.colorStrength / 100
                  for (let i = 0; i < data.length; i += 4) {
                    if (data[i + 3] > 0) { // 只处理非透明像素
                      data[i] = data[i] * (1 - strength) + r * strength
                      data[i + 1] = data[i + 1] * (1 - strength) + g * strength
                      data[i + 2] = data[i + 2] * (1 - strength) + b * strength
                    }
                  }

                  // 将修改后的数据放回画布
                  exportCtx.putImageData(imageData, -width / 2, -height / 2)
                }

                // 恢复上下文状态
                exportCtx.restore()

                resolve()
              }
              img.onerror = () => {
                console.error(`Failed to load image: ${imageItem.src}`)
                resolve() // 即使图片加载失败也继续
              }
              img.src = imageItem.src
            })
          }))
        }

        // 等待所有图片加载完成后下载
        loadImages().then(() => {
          // 下载画布内容
          const link = document.createElement('a')
          link.download = 'weather_map.png'
          link.href = exportCanvas.toDataURL('image/png')
          link.click()
        }).catch((error) => {
          console.error('导出图片时出错：', error)
          this.$message.error('导出图片失败，请重试')
        })
      } catch (error) {
        console.error('导出画布时出错：', error)
        this.$message.error('导出图片失败，请重试')
      }
    },

    // 立即更新预览图像（不使用防抖）
    updatePreviewImageImmediate() {
      // 在样式改变后立即更新预览
      this.$nextTick(() => {
        this.updatePreviewImage()
      })
    },

    handleInputChange(field, value) {
      console.log('handleInputChange 被调用:', { field, value })
      this[field] = value
    },

    // 预留updateLegendBackground方法以保持兼容性
    updateLegendBackground() {
      // 由于图例设置被移除，此方法保持为空
      // 因为有其他地方可能会调用这个方法
    },

    // 处理字体名称，确保格式正确
    formatFontFamily(fontName) {
      // 所有字体名称都加引号，确保CSS渲染正确
      return `'${fontName}'`
    },

    // 清除所有已绘制的天气落区
    clearWeatherAreas() {
      // 如果正在删除或绘制，先停止
      if (this.isDeleting) {
        this.stopDeleting()
      }

      if (this.isDrawing) {
        this.stopDrawing()
      }

      // 确认是否清除
      this.$confirm('确定要清除所有已绘制的天气落区吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清空数据源
        this.weatherSource.clear()
        // 触发重绘
        this.weatherLayer.changed()
        // 清空已绘制的天气类型
        this.drawnWeatherTypes = []
        this.$message.success('已清除所有天气落区')
      }).catch(() => {
        this.$message.info('已取消清除')
      })
    },

    // 添加自定义文字
    addCustomText() {
      this.customTexts.push({
        content: '自定义文字',
        position: { x: 300, y: 300 }, // 默认位置
        style: {
          fontSize: 18,
          fontFamily: 'SimHei',
          color: '#000000'
        }
      })
    },

    // 删除自定义文字
    removeCustomText(index) {
      this.customTexts.splice(index, 1)
      // 如果在预览模式下，更新预览图
      if (this.showPreview) {
        this.updatePreviewImage()
      }
    },

    // 选择图片
    selectImage(image) {
      this.selectedImage = image
    },

    // 添加图片到画布
    addImageToCanvas() {
      if (!this.selectedImage) return

      const canvas = this.$refs.previewCanvas
      if (!canvas) return

      // 获取画布中心点
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2

      // 添加图片到画布
      this.canvasImages.push({
        name: this.selectedImage.name,
        src: this.selectedImage.src,
        path: this.selectedImage.path,
        size: this.imageSize,
        rotation: 0,
        color: '#ffffff',
        colorStrength: 0,
        position: {
          x: centerX - this.imageSize / 2,
          y: centerY - this.imageSize / 2
        }
      })

      // 自动选中新添加的图片
      this.selectedCanvasImage = this.canvasImages.length - 1

      // 更新预览
      this.updatePreviewImage()
    },

    // 自动添加指南针图片到左上角
    addCompassToCanvas() {
      console.log('开始添加指南针到画布')

      // 查找指南针图片
      const compassImage = this.drawImages.find(image => image.name === '指南针')
      if (!compassImage) {
        console.warn('未找到指南针图片')
        return
      }

      // 检查是否已经添加了指南针
      const existingCompass = this.canvasImages.find(image => image.name === '指南针')
      if (existingCompass) {
        console.log('指南针已存在，跳过添加')
        return
      }

      // 添加指南针到画布（位置会在 doUpdatePreviewImage 中正确设置）
      const compassData = {
        name: compassImage.name,
        src: compassImage.src,
        path: compassImage.path,
        size: 60, // 指南针大小
        rotation: 0,
        color: '#ffffff',
        colorStrength: 0,
        position: {
          x: 20, // 临时位置，会在 doUpdatePreviewImage 中重新设置
          y: 20 // 临时位置，会在 doUpdatePreviewImage 中重新设置
        }
      }

      this.canvasImages.push(compassData)

      console.log('已自动添加指南针，位置将在预览更新时设置')
    },

    // 选择已添加的图片
    selectCanvasImage(index) {
      this.selectedCanvasImage = index
    },

    // 删除图片
    deleteImage(index) {
      if (index >= 0 && index < this.canvasImages.length) {
        this.canvasImages.splice(index, 1)
        // 如果删除的是当前选中的图片，取消选择
        if (this.selectedCanvasImage === index) {
          this.selectedCanvasImage = null
        } else if (this.selectedCanvasImage > index) {
          // 如果删除的是前面的图片，调整选中索引
          this.selectedCanvasImage--
        }
        // 更新预览
        this.updatePreviewImage()
      }
    },

    // 旋转图片
    rotateImage(index, degrees) {
      if (index >= 0 && index < this.canvasImages.length) {
        const image = this.canvasImages[index]
        image.rotation = (image.rotation || 0) + degrees
        // 更新预览
        this.updatePreviewImage()
      }
    },

    // 调整图片大小
    resizeImage(index, delta) {
      if (index >= 0 && index < this.canvasImages.length) {
        const image = this.canvasImages[index]
        // 限制最小和最大尺寸
        const newSize = Math.max(20, Math.min(300, image.size + delta))
        image.size = newSize
        // 更新预览
        this.updatePreviewImage()
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}年${month}月${day}日`
    },
    // 删除图层
    deleteWeatherFeature() {
      if (this.isDeleting) {
        this.stopDeleting()
        return
      }

      // 停止绘制（如果正在进行）
      if (this.isDrawing) {
        this.stopDrawing()
      }

      this.isDeleting = true

      // 添加点击事件监听器
      const clickListener = this.map.on('click', (event) => {
        // 获取点击位置的像素坐标
        const pixel = event.pixel

        // 检查是否点击了一个天气特征
        const feature = this.map.forEachFeatureAtPixel(pixel, (feature) => {
          // 确保只返回天气图层的特征
          if (feature.get('weatherType') !== undefined) {
            return feature
          }
          return null
        })

        if (feature) {
          // 确认删除
          this.$confirm(`确定要删除该${this.getWeatherTypeName(feature.get('weatherType'))}落区吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 从图层中移除该特征
            this.weatherSource.removeFeature(feature)
            this.$message.success('已删除选中的落区')

            // 更新已绘制的天气类型
            this.drawnWeatherTypes = this.getDrawnWeatherTypes()
          }).catch(() => {
            this.$message.info('已取消删除')
          })
        } else {
          this.$message.info('未选中任何落区，请点击要删除的区域')
        }
      })

      // 保存点击监听器，以便稍后移除
      this.deleteInteraction = clickListener

      // 更改鼠标样式，以指示删除模式
      document.getElementById('map').style.cursor = 'crosshair'
    },

    // 停止删除模式
    stopDeleting() {
      this.isDeleting = false

      // 移除点击事件监听器
      if (this.deleteInteraction) {
        unByKey(this.deleteInteraction)
        this.deleteInteraction = null
      }

      // 恢复默认鼠标样式
      document.getElementById('map').style.cursor = 'default'
    },

    // 获取天气类型的中文名称
    getWeatherTypeName(type) {
      const weatherType = this.weatherTypes.find(item => item.value === type)
      return weatherType ? weatherType.label : '天气'
    },
    // 获取已绘制的天气类型
    getDrawnWeatherTypes() {
      const drawnTypes = []
      this.weatherSource.getFeatures().forEach(feature => {
        const type = feature.get('weatherType')
        if (!drawnTypes.includes(type)) {
          drawnTypes.push(type)
        }
      })
      return drawnTypes
    },
    updateSubtitle() {
      // 验证输入值的有效性
      if (!this.startMonth || this.startMonth < 1 || this.startMonth > 12) {
        this.startMonth = new Date().getMonth() + 1
      }

      if (!this.startDay || this.startDay < 1 || this.startDay > 31) {
        this.startDay = new Date().getDate()
      }

      if (!this.startHour || this.startHour < 0 || this.startHour > 23) {
        this.startHour = new Date().getHours()
      }

      // 创建起始日期对象
      const currentYear = new Date().getFullYear()
      const startDate = new Date(currentYear, this.startMonth - 1, this.startDay, this.startHour)

      // 计算24小时后的日期和时间
      const endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000)

      this.endMonth = endDate.getMonth() + 1
      this.endDay = endDate.getDate()
      this.endHour = endDate.getHours()

      // 更新副标题文本
      const subtitle = `${this.startMonth}月${this.startDay}日${this.startHour}时—${this.endMonth}月${this.endDay}日${this.endHour}时`
      this.previewSubtitle = subtitle

      // 更新预览
      this.updatePreviewImage()

      // 返回生成的副标题
      return subtitle
    },
    openStationDialog() {
      // 设置加载状态，避免用户重复点击
      this.stationLoading = true

      // 不再每次请求数据，而是使用已加载的站点数据
      this.stationSearchValue = ''
      this.stationFilterValue = 'all'

      // 如果还没有加载站点数据（可能初始化时加载失败），则重新加载一次
      if (this.stationList.length === 0) {
        this.fetchStationData()
      } else {
        // 直接使用已有数据，重置过滤列表
        this.filteredStationList = [...this.stationList]

        // 设置加载完成
        this.stationLoading = false
      }

      // 显示弹窗
      this.stationDialogVisible = true

      // 使用nextTick确保弹窗DOM已渲染完成
      this.$nextTick(() => {
        // 同步已选站点到表格选择状态
        this.syncSelectedStationsToTable()
      })
    },

    // 新增方法：同步已选站点到表格
    syncSelectedStationsToTable() {
      // 确保表格已渲染
      if (!this.$refs.stationTable) {
        return
      }

      // 先清除所有选择
      this.$refs.stationTable.clearSelection()

      // 检查是否有已选的站点
      const selectedCount = this.selectedStations.length
      const mapStationCount = this.mapStations.length

      // 决定使用哪个列表作为已选站点的来源
      let selectedIds = []

      if (mapStationCount > 0) {
        // 优先使用已添加到地图的站点
        selectedIds = this.mapStations.map(station => station.id)
      } else if (selectedCount > 0) {
        // 如果没有添加到地图的站点，但有选中的站点
      } else {
        return
      }

      // 更新选中状态
      let selectedCount2 = 0

      // 遍历站点列表中的每一项，检查是否应该选中
      this.filteredStationList.forEach(station => {
        if (selectedIds.includes(station.stationIdC)) {
          // 选中站点
          this.$refs.stationTable.toggleRowSelection(station, true)
          selectedCount2++
        }
      })
    },

    // 从站点数据中生成县区筛选选项
    generateCountyOptions() {
      try {
        // 确保有站点数据
        if (!this.stationList || this.stationList.length === 0) {
          console.warn('站点列表为空，无法生成县区选项')
          this.countyOptions = [{ value: 'all', label: '全部' }]
          return
        }

        // 收集所有不同的县区
        const counties = this.stationList
          .map(station => station.cnty)
          .filter(county => county && county.trim() !== '') // 过滤空值
          .filter((county, index, self) => self.indexOf(county) === index) // 去重

        // 生成选项，使用实际县区名称作为value
        this.countyOptions = [
          { value: 'all', label: '全部' },
          ...counties.map(county => ({
            value: county.toLowerCase(), // 值使用小写
            label: county // 显示原始名称
          }))
        ]
      } catch (error) {
        this.countyOptions = [{ value: 'all', label: '全部' }]
      }
    },
    handleStationSelectionChange(val) {
      // 保存选中的站点
      this.selectedStations = val

      // 更新选中站点的ID列表，用于持久化选择状态
      this.selectedStationIds = val.map(station => station.stationIdC)
    },
    addStationsToMap() {
      // 关闭站点弹窗
      this.stationDialogVisible = false

      // 清除现有站点，避免重复添加
      this.mapStations = []

      // 清除地图上的站点
      if (this.mapStationSource) {
        this.mapStationSource.clear()
      }
      this.selectedMapStations = []

      // 如果没有选择任何站点，相当于清除所有站点，直接返回
      if (this.selectedStations.length === 0) {
        this.selectedStationIds = []
        this.updatePreviewImage()
        return
      }

      // 记录已勾选的站点ID
      this.selectedStationIds = this.selectedStations.map(station => station.stationIdC)

      // 将选中的站点添加到地图和预览列表
      this.selectedStations.forEach(station => {
        // 添加到地图
        this.addStationToMap(station)

        // 提取站点信息，确保数据有效
        const lon = parseFloat(station.lon)
        const lat = parseFloat(station.lat)

        // 创建一个站点对象
        const stationObj = {
          name: station.stationName || `站点${this.mapStations.length + 1}`,
          id: station.stationIdC || `ID${this.mapStations.length + 1}`,
          cnty: station.cnty || '中国',
          position: {
            // 初始位置设为屏幕中心，避免起始时看不到
            x: 300,
            y: 300
          },
          style: {
            fontSize: 14,
            color: '#1890ff'
          },
          lon: lon || 116, // 无效时使用北京经度
          lat: lat || 40 // 无效时使用北京纬度
        }

        // 添加到站点数组
        this.mapStations.push(stationObj)
      })

      // 更新预览图
      this.updatePreviewImage()
    },
    // 修正站点位置计算方法，调整坐标系方向
    calculateStationX(longitude) {
      // 获取预览画布
      const canvas = this.$refs.previewCanvas
      if (!canvas) {
        console.error('计算站点X坐标时无法获取画布')
        return 300 // 默认值
      }

      // 使用固定坐标映射，中国大致经度范围：73°E - 135°E
      const minLon = 73
      const maxLon = 135

      // 计算经度在范围内的相对位置 (0-1)
      const ratio = (longitude - minLon) / (maxLon - minLon)

      // 将相对位置映射到画布上，考虑边距
      // 注意这里x轴方向是从左到右，所以不需要反转
      const padding = 50
      return padding + ratio * (canvas.width - padding * 2)
    },

    calculateStationY(latitude) {
      // 获取预览画布
      const canvas = this.$refs.previewCanvas
      if (!canvas) {
        console.error('计算站点Y坐标时无法获取画布')
        return 300 // 默认值
      }

      // 使用固定坐标映射，中国大致纬度范围：15°N - 53°N
      const minLat = 15
      const maxLat = 53

      // 计算纬度在范围内的相对位置 (0-1)
      // 注意这里需要反转，因为纬度越高，y坐标值越小
      const ratio = 1 - (latitude - minLat) / (maxLat - minLat)

      // 将相对位置映射到画布上，考虑边距
      const padding = 50
      return padding + ratio * (canvas.height - padding * 2)
    },
    // 添加坐标转换辅助方法
    toWGS84(coord) {
      // 从EPSG:3857转换到WGS84经纬度
      try {
        // 如果已经安装了ol库的坐标转换函数，使用它
        if (typeof fromLonLat === 'function') {
          const wgs84 = fromLonLat([coord, 0], 'EPSG:4326')
          return wgs84[0]
        }

        // 简易转换方法
        return coord * 180 / 20037508.34
      } catch (e) {
        console.error('坐标转换出错:', e)
        return coord
      }
    },
    // 添加选择自定义文本的方法
    selectCustomText(index) {
      this.selectedCustomText = index
      // 如果是站点，显示站点信息
      if (this.customTexts[index].style.isStation) {
        this.selectedCanvasImage = null // 取消选择图片
      }
    },
    // 添加站点选择方法
    selectStation(index) {
      this.selectedStation = index
      // 取消其他选择
      this.selectedCustomText = null
      this.selectedCanvasImage = null
    },
    // 添加站点删除方法
    removeStation(index) {
      if (index !== null && index >= 0 && index < this.mapStations.length) {
        // 从已选ID列表中移除
        const stationId = this.mapStations[index].id
        const idIndex = this.selectedStationIds.indexOf(stationId)
        if (idIndex > -1) {
          this.selectedStationIds.splice(idIndex, 1)
        }

        // 从站点列表中移除
        this.mapStations.splice(index, 1)

        // 重置选中状态
        this.selectedStation = null

        // 更新预览图片
        this.updatePreviewImage()
      }
    },
    // 新增绘制站点的方法
    drawStationsOnCanvas() {
      const canvas = this.$refs.previewCanvas
      if (!canvas) {
        return
      }

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        return
      }

      // 清除可能的旧绘制内容
      ctx.save()

      this.mapStations.forEach(station => {
        // 先检查站点位置是否合理
        if (station.position.x < 0 || station.position.x > canvas.width ||
                    station.position.y < 0 || station.position.y > canvas.height) {
          // 修正到合理范围
          station.position.x = Math.max(10, Math.min(canvas.width - 10, station.position.x))
          station.position.y = Math.max(10, Math.min(canvas.height - 10, station.position.y))
        }

        // 绘制绿色点
        ctx.beginPath()
        ctx.arc(station.position.x, station.position.y, 3, 0, Math.PI * 2)
        ctx.fillStyle = '#67c23a' // 绿色
        ctx.fill()

        // 移除站点名称绘制，因为站点名称已经在地图上显示
      })

      // 恢复画布状态
      ctx.restore()
    },
    // 添加更新站点样式的方法
    updateStationStyle(property, value) {
      if (this.selectedStation !== null && this.mapStations[this.selectedStation]) {
        if (!this.mapStations[this.selectedStation].style) {
          this.mapStations[this.selectedStation].style = {}
        }

        // 更新样式属性
        this.$set(this.mapStations[this.selectedStation].style, property, value)

        // 如果是颜色属性，同时更新文本颜色
        if (property === 'color') {
          this.$set(this.mapStations[this.selectedStation], 'textColor', value)
        }

        // 如果是字体大小，转换为数字
        if (property === 'fontSize') {
          const fontSize = parseInt(value, 10)
          if (!isNaN(fontSize)) {
            this.$set(this.mapStations[this.selectedStation], 'fontSize', fontSize)
          }
        }

        // 更新预览
        this.$nextTick(() => {
          this.updatePreviewImage()
        })
      }
    },
    // 添加预览图片更新的具体实现方法
    doUpdatePreviewImage() {
      if (!this.generatedImageData) {
        return
      }

      const canvas = this.$refs.previewCanvas
      if (!canvas) {
        console.error('预览画布未找到')
        return
      }

      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // 设置画布尺寸为预览容器的尺寸
        const container = canvas.parentElement
        if (!container || container.clientWidth <= 0 || container.clientHeight <= 0) {
          console.error('预览容器尺寸无效')
          return
        }

        // 设置画布尺寸
        canvas.width = container.clientWidth
        canvas.height = container.clientHeight

        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // 计算图片缩放比例，保持边界图像在预览中完整可见
        const margin = 40 // 增加边距，确保边界不会贴近画布边缘
        const maxWidth = canvas.width - margin * 2
        const maxHeight = canvas.height - margin * 2

        // 确保图像完全在画布内，使用最小缩放比例
        const scale = Math.min(
          maxWidth / img.width,
          maxHeight / img.height
        ) * 0.9 // 再缩小一点，留出更多安全边距

        // 计算居中位置
        const scaledWidth = img.width * scale
        const scaledHeight = img.height * scale
        const x = (canvas.width - scaledWidth) / 2
        const y = (canvas.height - scaledHeight) / 2

        // 绘制边界图片
        ctx.drawImage(img, x, y, scaledWidth, scaledHeight)

        // 仅在首次加载时更新标题和副标题的位置
        if (this.firstLoad) {
          // 标题位于地图区域上方，居中并进一步向上移
          this.titlePosition = { x: canvas.width / 2, y: y / 2 }
          // 副标题位于标题下方，增加与标题的距离
          this.subtitlePosition = { x: canvas.width / 2, y: this.titlePosition.y + 40 }
          // 设置落款位置在副标题下方，与标题和副标题在垂直方向上中心轴对齐
          this.signaturePosition = { x: canvas.width / 2, y: this.subtitlePosition.y + 40 }
          // 设置图例位置（左下角）
          this.legendPosition = { x: x + 10, y: y + scaledHeight - 100 }
          // 设置审图号位置（右下角，向左移）
          this.mapApprovalNumberPosition = { x: canvas.width - 250, y: canvas.height - 50 }
          // 设置发布时间位置（审图号上方）并确保与审图号左侧对齐
          this.publishTimePosition = { x: this.mapApprovalNumberPosition.x, y: canvas.height - 80 }

          // 初始化自定义文字位置
          // 如果有自定义文字，均匀分布在画布中间区域
          if (this.customTexts.length > 0) {
            const centerY = y + scaledHeight / 2
            const spacing = scaledWidth / (this.customTexts.length + 1)

            this.customTexts.forEach((textItem, index) => {
              // 将文字均匀分布在中间区域
              textItem.position = {
                x: x + spacing * (index + 1),
                y: centerY
              }
            })
          }

          // 初始化图片位置
          if (this.canvasImages.length > 0) {
            this.canvasImages.forEach((imageItem, index) => {
              // 特殊处理指南针，放在左上角
              if (imageItem.name === '指南针') {
                imageItem.position = {
                  x: x - 280, // 地图左边缘 + 20px
                  y: y - 40 // 地图顶部边缘 + 20px
                }
              } else {
                // 其他图片按原来的逻辑分布
                const otherImages = this.canvasImages.filter(img => img.name !== '指南针')
                const spacing = scaledWidth / (otherImages.length + 1)
                const centerY = y + scaledHeight / 3 // 放置在上部1/3处
                const otherIndex = otherImages.findIndex(img => img === imageItem)

                imageItem.position = {
                  x: x + spacing * (otherIndex + 1) - imageItem.size / 2,
                  y: centerY - imageItem.size / 2
                }
              }
            })
          }

          // 在首次加载时更新站点位置并只绘制一次站点
          if (this.mapStations.length > 0) {
            // 计算预览图区域的边界
            const mapBounds = {
              x: x,
              y: y,
              width: scaledWidth,
              height: scaledHeight
            }

            // 检查是否已有从地图获取的站点位置
            if (this.tempMapInfo) {
              // 修正站点位置，使它们适应当前预览画布的尺寸和位置
              this.mapStations.forEach(station => {
                // 临时地图与预览画布的尺寸比例
                const widthRatio = scaledWidth / this.tempMapInfo.width
                const heightRatio = scaledHeight / this.tempMapInfo.height

                // 计算位置偏移量
                const adjustedX = x + (station.position.x * widthRatio)
                const adjustedY = y + (station.position.y * heightRatio)

                // 更新站点位置
                station.position = {
                  x: adjustedX,
                  y: adjustedY
                }
              })
            } else {

            }

            // 只在首次加载时绘制站点
            this.$nextTick(() => {
              this.drawStationsOnCanvas()
            })
          }

          this.firstLoad = false

          // 触发一次DOM更新
          this.$forceUpdate()
        } else if (this.stationsNeedUpdate) {
          // 只有当站点需要更新时才重新绘制站点
          // 比如拖动站点后需要更新
          this.$nextTick(() => {
            this.drawStationsOnCanvas()
          })
          this.stationsNeedUpdate = false
        }

        // 确保标题和副标题在正确的位置显示
        this.$nextTick(() => {
          this.updateCenteredTitles()
        })
      }

      img.src = this.generatedImageData
    },
    // 添加初始化站点图层的方法
    initStationLayer() {
      // 创建站点数据源
      this.mapStationSource = new SourceVector()

      // 创建站点图层
      this.mapStationLayer = new LayerVector({
        source: this.mapStationSource,
        style: (feature) => {
          // 创建站点样式
          return new Style({
            image: new CircleStyle({
              radius: 2, // 小圆点半径从3改为2
              fill: new Fill({
                color: '#67c23a' // 统一使用绿色，包括特殊站点
              }),
              stroke: new Stroke({
                color: 'black',
                width: 1
              })
            }),
            text: new Text({
              text: feature.get('name'),
              font: 'bold 12px SimHei',
              offsetY: -12,
              fill: new Fill({
                color: 'black'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 2
              })
            })
          })
        },
        // 设置站点图层的z-index，确保在边界线上方
        zIndex: 12
      })

      // 将站点图层添加到地图
      if (this.map) {
        this.map.addLayer(this.mapStationLayer)
      }
    },
    // 添加在地图上添加站点的方法
    addStationToMap(station) {
      if (!this.mapStationSource) {
        return
      }

      // 解析站点经纬度
      const lon = parseFloat(station.lon)
      const lat = parseFloat(station.lat)

      if (isNaN(lon) || isNaN(lat)) {
        return
      }

      // 创建几何特征
      const geometry = new Point(fromLonLat([lon, lat]))

      // 创建特征对象
      const feature = new Feature({
        geometry: geometry,
        name: station.stationName || station.name,
        id: station.stationIdC || station.id,
        lon: lon,
        lat: lat,
        special: (station.stationName === '磴口' || station.stationName === '磴口县' ||
                         station.name === '磴口' || station.name === '磴口县')
      })

      // 设置特征ID
      feature.setId(station.stationIdC || station.id || Date.now().toString())

      // 添加到数据源
      this.mapStationSource.addFeature(feature)

      // 记录到已选择的站点列表
      this.selectedMapStations.push({
        id: station.stationIdC || station.id,
        name: station.stationName || station.name,
        lon: lon,
        lat: lat
      })

      // 缓存特征对象
      this.stationFeatures[station.stationIdC || station.id] = feature
    },
    // 初始化预览数据
    initializePreviewData() {
      // 获取合适的城市名称
      const cityName = this.cityName || '内蒙古'

      // 设置主标题
      const titleSuffix = this.getWeatherTitleSuffix(this.weatherType)
      const newTitle = cityName + titleSuffix

      // 更新当前时间
      const now = new Date()
      this.startMonth = now.getMonth() + 1
      this.startDay = now.getDate()
      this.startHour = now.getHours()

      // 计算24小时后
      const later = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      this.endMonth = later.getMonth() + 1
      this.endDay = later.getDate()
      this.endHour = later.getHours()

      // 设置副标题
      const newSubtitle = `${this.startMonth}月${this.startDay}日${this.startHour}时—${this.endMonth}月${this.endDay}日${this.endHour}时`

      // 设置落款
      const newSignature = cityName + '气象台'

      // 设置发布时间
      const newPublishTime = `${this.startMonth}月${this.startDay}日${this.startHour}时发布`

      // 直接设置新值，确保默认信息正确显示
      this.previewTitle = newTitle
      this.previewSubtitle = newSubtitle
      this.signatureText = newSignature
      this.publishTime = newPublishTime

      // 确保审图号有值
      if (!this.mapApprovalNumber) {
        this.mapApprovalNumber = '底图审图号:蒙S(2019)33号'
      }

      // 设置显示标志
      this.previewShowLegend = true
      this.previewShowSignature = true
      this.showMapApprovalNumber = true
      this.showPublishTime = true
    },

    // 测试方法：检查当前数据状态
    debugCurrentData() {
      console.log('=== 当前数据状态检查 ===')
      console.log('previewTitle:', this.previewTitle)
      console.log('previewSubtitle:', this.previewSubtitle)
      console.log('signatureText:', this.signatureText)
      console.log('publishTime:', this.publishTime)
      console.log('mapApprovalNumber:', this.mapApprovalNumber)
      console.log('showPreview:', this.showPreview)

      // 检查DOM中的实际值
      const titleInput = document.querySelector('.preview-controls input[placeholder="请输入主标题"]')
      const subtitleInput = document.querySelector('.preview-controls input[placeholder="请输入副标题内容"]')
      const signatureInput = document.querySelector('.preview-controls input[placeholder="请输入落款信息"]')
      const publishTimeInput = document.querySelector('.preview-controls input[placeholder="选择发布时间"]')

      console.log('DOM中输入框的值:')
      console.log('主标题输入框值:', titleInput ? titleInput.value : '未找到')
      console.log('副标题输入框值:', subtitleInput ? subtitleInput.value : '未找到')
      console.log('落款输入框值:', signatureInput ? signatureInput.value : '未找到')
      console.log('发布时间输入框值:', publishTimeInput ? publishTimeInput.value : '未找到')
      console.log('========================')
    },

    // 仅在组件初始化时同步一次输入框值，避免覆盖用户输入
    forceUpdateInputValues() {
      // 如果预览对话框不可见，不进行同步
      if (!this.showPreview) {
        return
      }

      this.$nextTick(() => {
        // 等待DOM完全渲染后再设置值
        setTimeout(() => {
          // 尝试多种选择器来找到输入框
          let inputs = document.querySelectorAll('.preview-controls .ivu-input')
          if (inputs.length === 0) {
            inputs = document.querySelectorAll('.preview-controls input')
          }
          if (inputs.length === 0) {
            inputs = document.querySelectorAll('.control-group input')
          }

          if (inputs.length >= 5) {
            // 根据实际DOM结构重新映射输入框
            const textInputs = Array.from(inputs).filter(input => input.type === 'text')

            if (textInputs.length >= 5) {
              // 只在输入框为空时才设置默认值，避免覆盖用户输入
              if (!textInputs[0].value) textInputs[0].value = this.previewTitle || ''
              if (!textInputs[1].value) textInputs[1].value = this.previewSubtitle || ''
              if (!textInputs[2].value) textInputs[2].value = this.signatureText || ''
              if (!textInputs[3].value) textInputs[3].value = this.mapApprovalNumber || ''
              if (!textInputs[4].value) textInputs[4].value = this.publishTime || ''
            } else {
              // 如果文本输入框数量不够，尝试通过placeholder定位
              const titleInput = Array.from(inputs).find(input =>
                input.placeholder && input.placeholder.includes('主标题'))
              const subtitleInput = Array.from(inputs).find(input =>
                input.placeholder && input.placeholder.includes('副标题'))
              const signatureInput = Array.from(inputs).find(input =>
                input.placeholder && input.placeholder.includes('落款'))
              const approvalInput = Array.from(inputs).find(input =>
                input.placeholder && input.placeholder.includes('批准文号'))
              const publishTimeInput = Array.from(inputs).find(input =>
                input.placeholder && input.placeholder.includes('发布时间'))

              // 只在输入框为空时才设置默认值
              if (titleInput && !titleInput.value) titleInput.value = this.previewTitle || ''
              if (subtitleInput && !subtitleInput.value) subtitleInput.value = this.previewSubtitle || ''
              if (signatureInput && !signatureInput.value) signatureInput.value = this.signatureText || ''
              if (approvalInput && !approvalInput.value) approvalInput.value = this.mapApprovalNumber || ''
              if (publishTimeInput && !publishTimeInput.value) publishTimeInput.value = this.publishTime || ''
            }
          }
        }, 200)
      })
    },

    // 添加获取天气类型对应标题后缀的辅助方法
    getWeatherTitleSuffix(type) {
      switch (type) {
        case 'precipitation':
          return '降水预报图'
        case 'sleet':
          return '雨夹雪预报图'
        case 'snow':
          return '降雪预报图'
        case 'wind':
          return '大风预报图'
        case 'sandstorm':
          return '沙尘暴预警图'
        case 'frost':
          return '霜冻预警图'
        case 'cooling':
          return '降温预报图'
        case 'high_temperature':
          return '高温预报图'
        case 'strong_convection':
          return '强对流天气预报图'
        default:
          return '天气预报图'
      }
    },
    filterStations() {
      if (!this.stationList || this.stationList.length === 0) {
        console.warn('站点列表为空，无法筛选')
        this.filteredStationList = []
        return
      }

      // 保存当前选中的站点ID，用于后续恢复选择状态
      const selectedIds = this.selectedStations.map(station => station.stationIdC)

      this.filteredStationList = this.stationList.filter(station => {
        // 搜索逻辑 - 站点名称或站号包含搜索词
        let nameMatch = true
        let countyMatch = true

        // 如果有搜索词，进行名称和站号搜索
        if (this.stationSearchValue && this.stationSearchValue.trim() !== '') {
          const searchTerm = this.stationSearchValue.toLowerCase().trim()
          const stationName = (station.stationName || '').toLowerCase()
          const stationId = (station.stationIdC || '').toLowerCase()

          nameMatch = stationName.includes(searchTerm) || stationId.includes(searchTerm)
        }

        // 如果选择了特定县区，进行县区筛选
        if (this.stationFilterValue && this.stationFilterValue !== 'all') {
          const countyName = (station.cnty || '').toLowerCase()
          countyMatch = countyName === this.stationFilterValue
        }

        // 同时满足搜索条件和县区筛选条件
        return nameMatch && countyMatch
      })

      // 筛选完成后，恢复已选站点的选中状态
      this.$nextTick(() => {
        if (this.$refs.stationTable && selectedIds.length > 0) {
          // 先清除所有选择，避免状态混乱
          this.$refs.stationTable.clearSelection()

          // 重新选中那些仍然在筛选结果中的已选站点
          this.filteredStationList.forEach(station => {
            if (selectedIds.includes(station.stationIdC)) {
              this.$refs.stationTable.toggleRowSelection(station, true)
            }
          })

          // 检查是否有选中的站点在筛选后不可见
          const visibleSelectedCount = this.filteredStationList.filter(
            station => selectedIds.includes(station.stationIdC)
          ).length
        }
      })
    },
    clearStationSelection() {
      // 清除表格中的选择状态
      if (this.$refs.stationTable) {
        this.$refs.stationTable.clearSelection()
      }

      // 清空选中站点数组，但不影响已添加到地图的站点
      this.selectedStations = []

      // 取消选中的单个站点
      this.selectedStation = null
      this.selectedCanvasImage = null
    },
    handleCloseStationDialog(done) {
      done()
    }
  }
}

</script>

<style scoped>
#picture-map {
    width: 100%;
    height: 100%;
    position: relative;
}

#map {
    width: 100%;
    height: 100vh;
    position: relative;
}

.draw-menu {
    position: absolute;
    top: 50px;
    right: 10px;
    width: 280px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 15px;
    z-index: 2002;
}

.menu-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.menu-item {
    margin-bottom: 15px;
}

.item-title {
    font-size: 14px;
    margin-bottom: 5px;
    color: #666;
}

.item-content {
    width: 100%;
}

.item-content select,
.item-content input {
    width: 100%;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 10px;
    box-sizing: border-box;
}

.weather-levels {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
}

.weather-level-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.weather-level-item:last-child {
    border-bottom: none;
}

.weather-level-item:hover {
    background-color: #f5f7fa;
}

.weather-level-item.active {
    background-color: #ecf5ff;
    color: #409eff;
}

.level-name {
    flex: 1;
}

.color-block {
    width: 20px;
    height: 20px;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    cursor: pointer;
}

.color-picker {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    padding: 0;
    margin: 0;
    pointer-events: none;
}

.draw-btn {
    width: 100%;
    height: 36px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
}

.draw-btn:hover {
    background-color: #66b1ff;
}

.draw-btn.active {
    background-color: #e6a23c;
}

.draw-tip {
    font-size: 12px;
    color: #e6a23c;
    text-align: center;
    margin-top: 5px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
}

.checkbox-group label {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-right: 5px;
}

.generate-btn {
    width: 100%;
    height: 36px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.generate-btn:hover {
    background-color: #66b1ff;
}

.preview-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.preview-content {
    background-color: #fff;
    border-radius: 4px;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.preview-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h3 {
    margin: 0;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.preview-body {
    padding: 15px;
    display: flex;
    gap: 20px;
    overflow: auto;
    height: calc(90vh - 130px); /* 增加预览区域的高度 */
}

.preview-canvas-container {
    flex: 2; /* 减小宽度占比，由原来的1变为2 */
    position: relative;
    border: 1px solid #ddd;
    background-color: #fff;
    overflow: hidden;
    min-height: 550px; /* 增加最小高度 */
    max-width: 800px; /* 限制最大宽度 */
    margin: 0 3px; /* 居中显示 */
    padding: 20px 0px;
    box-sizing: border-box;
}

.preview-canvas-container canvas {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
}

.preview-controls {
    width: 320px;
    flex-shrink: 0; /* 防止控制面板被压缩 */
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    overflow-y: auto; /* 添加滚动条以适应更多控件 */
}

.control-group {
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.control-group label {
    display: block;
    /* margin-bottom: 5px; */
    color: #666;
}

.control-group input[type="text"] {
    width: 100%;
    padding: 6px;
    margin-bottom: 5px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-sizing: border-box;
}

/* 添加样式控制组的样式 */
.style-group {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
    background-color: #ffffff;
}

.style-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #409eff;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 5px;
}

.style-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.style-item label {
    width: 80px;
    flex-shrink: 0;
    margin-bottom: 0;
}

.style-item input,
.style-item select {
    flex: 1;
    height: 30px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 8px;
}

.style-item input[type="color"] {
    width: 40px;
    padding: 0;
    cursor: pointer;
}

.style-item input[type="number"] {
    width: 60px;
}

.preview-title,
.preview-subtitle {
    position: absolute;
    z-index: 1000;
    pointer-events: auto;
    cursor: move;
    user-select: none;
    text-align: center;
    white-space: nowrap;
    padding: 5px 10px;
    background-color: rgba(255, 255, 255, 0);
    border-radius: 4px;
    transform: translateX(-50%); /* 使文本水平居中于定位点 */
}

.preview-title {
    font-weight: bold;
    font-size: 24px;
    font-family: SimHei;
    color: #000000;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.preview-subtitle {
    font-size: 18px;
    font-family: SimHei;
    color: #000000;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.draggable-legend {
    position: absolute;
    background-color: rgba(255, 255, 255, 0);
    padding: 10px;
    border-radius: 4px;
    min-width: 150px;
    z-index: 1000;
    cursor: move;
    pointer-events: auto;
}

.legend-title {
    font-weight: bold;
    font-size: 16px;
    font-family: SimHei;
    color: #000000;
    margin-bottom: 5px;
    text-align: center;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.legend-items.multi-type {
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 20px;
    justify-content: space-around;
}

.legend-type-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-family: SimHei;
    color: #000000;
}

.legend-type-title {
    margin-bottom: 5px;
    font-weight: bold;
}

.preview-footer {
    padding: 15px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancel-btn,
.download-btn {
    padding: 8px 20px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-btn {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    color: #666;
}

.download-btn {
    background-color: #409eff;
    border: none;
    color: #fff;
}

.cancel-btn:hover {
    background-color: #e4e7ed;
}

.download-btn:hover {
    background-color: #66b1ff;
}

/* 优化的滑块样式 */
.slider {
    width: 100%;
    height: 8px;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(135deg, #409eff, #1890ff);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    transition: all .2s ease;
    border: 1px solid rgba(64, 158, 255, 0.5);
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 6px rgba(0, 123, 255, 0.4);
}

.slider::-webkit-slider-thumb:active {
    background: linear-gradient(135deg, #1890ff, #096dd9);
}

.slider::-moz-range-track {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #e8f2ff, #d1e6ff);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #dcdfe6;
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: linear-gradient(135deg, #409eff, #1890ff);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    transition: all .2s ease;
    border: 1px solid rgba(64, 158, 255, 0.5);
}

.slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 6px rgba(0, 123, 255, 0.4);
}

.slider::-moz-range-thumb:active {
    background: linear-gradient(135deg, #1890ff, #096dd9);
}

.slider-value {
    min-width: 45px;
    text-align: right;
    color: #409eff;
    font-size: 13px;
    font-weight: 500;
}

.draggable-signature {
    position: absolute;
    padding: 5px;
    cursor: move;
    z-index: 2;
    user-select: none;
    text-align: center;
    transform: translateX(-50%); /* 使文本水平居中于定位点 */
}

/* 添加清除按钮样式 */
.clear-btn {
    width: 100%;
    height: 36px;
    background-color: #f56c6c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 5px;
}

.clear-btn:hover {
    background-color: #f78989;
}

.text-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.custom-text-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.custom-text-item input[type="text"] {
    flex: 1;
    margin-right: 5px;
}

.text-controls {
    display: flex;
    align-items: center;
}

.text-controls input[type="color"] {
    width: 25px;
    height: 25px;
    margin-right: 5px;
    padding: 0;
    border: 1px solid #dcdfe6;
}

.remove-text-btn {
    width: 24px;
    height: 24px;
    line-height: 20px;
    text-align: center;
    background-color: #f56c6c;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
}

.add-text-btn {
    width: 100%;
    height: 32px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.draggable-custom-text {
    position: absolute;
    padding: 5px;
    cursor: move;
    z-index: 2;
    user-select: none;
}

.custom-texts-control {
    margin-top: 5px;
}

.custom-text-control {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    flex-direction: column;
    border-bottom: 1px solid #dcdfe6;
}

.custom-text-control input[type="text"] {
    flex: 1;
    margin-right: 5px;
}

.custom-text-control input[type="color"] {
    width: 25px;
    height: 25px;
    margin-right: 5px;
    padding: 0;
    border: 1px solid #dcdfe6;
}

.custom-text-control .slider {
    width: 80px;
    margin-right: 5px;
}

.image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.gallery-item {
    width: 50px;
    height: 50px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: visible;
    background-color: #f5f7fa;
    transition: all 0.3s ease;
}

.gallery-item:hover {
    border-color: #409eff;
    background-color: #ecf5ff;
    transform: scale(1.05);
    z-index: 10;
}

.gallery-item img {
    max-width: 100%;
    max-height: 40px;
    object-fit: contain;
}

.gallery-item.active {
    border-color: #409eff;
    background-color: #ecf5ff;
}

.image-name-tooltip {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.gallery-item:hover .image-name-tooltip {
    opacity: 1;
    visibility: visible;
}

.image-name {
    font-size: 12px;
    margin-top: 3px;
    text-align: center;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

.image-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.add-image-btn {
    width: 100%;
    height: 32px;
    background-color: #67c23a;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.add-image-btn:hover {
    background-color: #85ce61;
}

.image-size-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.image-size-control label {
    flex-shrink: 0;
    width: 40px;
}

.image-size-control .slider {
    flex: 1;
}

.draggable-image {
    position: absolute;
    cursor: move;
    z-index: 2;
    user-select: none;
    box-sizing: border-box;
    border: 2px solid transparent;
}

.draggable-image.selected {
    border: 2px dashed #409eff;
}

.image-controls-overlay {
    display: none;
    position: absolute;
    top: -30px;
    left: 0;
    right: 0;
    text-align: center;
}

.image-edit-panel {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-edit-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-edit-row label {
    width: 70px;
    flex-shrink: 0;
}

.image-edit-row .slider {
    flex: 1;
}

.image-edit-row .color-picker {
    width: 30px;
    height: 30px;
    border: none;
    padding: 0;
    cursor: pointer;
}

.value-display {
    min-width: 40px;
    text-align: right;
    font-size: 12px;
    color: #666;
}

.close-panel-btn {
    background: none;
    border: none;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    float: right;
    padding: 0;
    margin: -3px;
}

.close-panel-btn:hover {
    color: #f56c6c;
}

.delete-btn {
    background-color: #f56c6c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    width: 100%;
}

.delete-btn:hover {
    background-color: #f78989;
}

/* ... existing styles ... */
.size-slider {
    display: flex;
    align-items: center;
    margin-top: 3px;
}

.size-slider label {
    font-size: 12px;
    color: #666;
    min-width: 65px;
}

.custom-texts-container {
    margin-top: 10px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 10px;
}

.text-controls {
    display: flex;
    align-items: center;
    margin-top: 5px;
    gap: 8px;
}

.control-group {
    margin-bottom: 0px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.control-group:last-child {
    border-bottom: none;
}
/* ... existing styles ... */

/* ... existing styles ... */
.draggable-approval-number {
    position: absolute;
    padding: 5px;
    cursor: move;
    z-index: 2;
    user-select: none;
    font-size: 12px;
    color: #000000;
    text-align: center;
    transform: translateX(-50%); /* 使文本水平居中于定位点 */
}

.draggable-publish-time {
    position: absolute;
    padding: 5px;
    cursor: move;
    z-index: 2;
    user-select: none;
    font-size: 12px;
    color: #000000;
    text-align: center;
    transform: translateX(-50%); /* 使文本水平居中于定位点 */
}
/* ... existing styles ... */

/* 清除所有按钮样式 */
.clear-all-btn {
    width: 100%;
    height: 36px;
    background-color: #E6A23C;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 5px;
}

.clear-all-btn:hover {
    background-color: #f89898;
}

.date-time-inputs {
    display: flex;
    align-items: center;
    gap: 5px;
}

.date-input {
    width: 40px;
    height: 30px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 5px;
    box-sizing: border-box;
}

.calculated-time {
    font-size: 12px;
    color: #666;
}

.full-width-input {
    width: 100%;
}

.control-label {
    margin-right: 10px;
}

.color-control {
    display: flex;
    align-items: center;
}

.size-control {
    display: flex;
    align-items: center;
}

.delete-control {
    display: flex;
    align-items: center;
}

.text-controls-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.text-content-row {
    margin-bottom: 8px;
}

.text-style-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

/* 结束样式定义 */

/* 站点弹窗样式优化 */
.station-dialog {
    z-index: 11001 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.station-dialog .el-dialog__header {
    background: linear-gradient(135deg, #3a8ee6, #5ca9f6);
    padding: 18px 24px;
    position: relative;
    border-bottom: none;
}

.station-dialog .el-dialog__title {
    color: white;
    font-weight: 600;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.station-dialog .el-dialog__headerbtn {
    top: 18px;
    right: 20px;
}

.station-dialog .el-dialog__headerbtn .el-dialog__close {
    color: rgba(255, 255, 255, 0.9);
    font-size: 20px;
    transition: all 0.3s;
}

.station-dialog .el-dialog__headerbtn:hover .el-dialog__close {
    color: #ffffff;
    transform: rotate(90deg);
}

.station-dialog .el-dialog__body {
    padding: 24px;
    background-color: #f9fafc;
}

.station-dialog .el-table {
    margin-bottom: 16px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.station-dialog .el-table th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
    padding: 12px 0;
}

.station-dialog .el-table td {
    padding: 10px 0;
}

.station-dialog .el-table--border,
.station-dialog .el-table--group {
    border-color: #ebeef5;
}

.station-dialog .el-table__row:hover > td {
    background-color: #ecf5ff !important;
}

.station-dialog .el-table__empty-block {
    min-height: 60px;
}

.station-dialog .el-dialog__footer {
    border-top: 1px solid #EBEEF5;
    padding: 16px 24px;
    display: flex;
    justify-content: flex-end;
    background-color: #ffffff;
}

.station-dialog .dialog-footer .el-button {
    padding: 10px 24px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s;
}

.station-dialog .dialog-footer .el-button+.el-button {
    margin-left: 12px;
}

.station-dialog .dialog-footer .el-button--primary {
    background: linear-gradient(135deg, #3a8ee6, #5ca9f6);
    border-color: #3a8ee6;
    box-shadow: 0 2px 6px rgba(58, 142, 230, 0.3);
}

.station-dialog .dialog-footer .el-button--primary:hover {
    background: linear-gradient(135deg, #5ca9f6, #3a8ee6);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(58, 142, 230, 0.4);
}

.station-dialog .empty-tip {
    text-align: center;
    color: #909399;
    padding: 30px 0;
    font-size: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.station-dialog .empty-tip:before {
    content: "📋";
    font-size: 32px;
    margin-bottom: 10px;
    opacity: 0.6;
}

/* 设置弹窗位置和大小 */
.el-dialog.station-dialog {
    position: fixed !important;
    top: 10vh !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    transition: all 0.3s !important;
}

/* 确保弹窗遮罩层次正确 */
.v-modal {
    z-index: 9998 !important;
}

/* 站点标记样式 */
.station-marker {
    position: absolute;
    padding-top: 14px;
    pointer-events: all;
    cursor: move;
    z-index: 100;
    text-align: center;
}

.station-dot {
    position: absolute;
    width: 4px; /* 从8px减小到4px */
    height: 4px; /* 从8px减小到4px */
    border-radius: 50%;
    background-color: #1890ff;
    border: 1px solid white; /* 从2px减小到1px */
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 2px rgba(0,0,0,0.3); /* 减小阴影 */
}

.station-name {
    font-size: 14px;
    font-family: 'SimHei';
    color: #1890ff;
    text-shadow: 0 0 2px white;
    white-space: nowrap;
}

.station-id {
    font-size: 10px;
    opacity: 0.8;
    text-align: center;
    margin-top: 2px;
    white-space: nowrap;
}

.station-marker.selected .station-dot {
    background-color: #f56c6c;
    width: 6px; /* 从10px减小到6px */
    height: 6px; /* 从10px减小到6px */
}

/* 站点信息面板样式 */
.station-info-panel {
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    margin-top: 5px;
}

.info-row {
    display: flex;
    margin-bottom: 8px;
}

.info-label {
    font-weight: bold;
    width: 80px;
}

.action-row {
    text-align: center;
    margin-top: 10px;
}

.remove-station-btn {
    background-color: #f56c6c;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.remove-station-btn:hover {
    background-color: #e64242;
}

/* 添加叠加站点按钮样式 */
.add-station-btn {
    width: 100%;
    height: 36px;
    background-color: #67c23a;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 5px;
}

.add-station-btn:hover {
    background-color: #85ce61;
}

.station-search-input {
    width: 100%;
    margin-bottom: 10px;
}

.station-filter-select {
    width: 100%;
}

.search-filter-bar {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
}

.station-search-input {
    flex: 2;
}

.station-filter-select {
    flex: 1;
}

.selected-count {
    font-weight: bold;
    color: #409EFF;
    margin: 0 4px;
}

.selection-info {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #606266;
    font-size: 13px;
}

.station-name-cell {
    font-weight: 500;
}

.station-dialog-content {
    display: flex;
    flex-direction: column;
}

.station-dialog .el-tag {
    border-radius: 12px;
    padding: 2px 10px;
}

.station-dialog .el-tag--info {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
}

.station-dialog .el-tag--success {
    background-color: #f0f9eb;
    border-color: #e1f3d8;
    color: #67c23a;
}

/* 性能优化选项样式 */
.performance-option {
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.performance-option label {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;
    cursor: pointer;
}

.performance-option input[type="checkbox"] {
    width: auto;
    margin-right: 6px;
    margin-bottom: 0;
}
</style>
