<template>
  <div class="base-container" @click="handleUserInteraction">
    <div id="map" />
    <div
      id="mouse-position"
      style="
        position: fixed;
        left: calc(50% - 130px);
        bottom: 10px;
        width: 260px;
        padding: 2px 10px 2px 10px;
        text-align: center;
      "
    />

    <!-- 左上角控制面板 -->
    <div :style="{ left: detectLeftShow ? '221px' : '12px' }" class="stationPanel" style="transition: left 0.5s;">
      <div style="display: flex;align-items: center;">
        <el-tooltip content="开启后，每10分钟刷新一次天气实况、雷达图、卫星图" placement="bottom" effect="light">
          <el-switch v-model="autoFlush" active-color="#52d2d2" active-text="自动刷新" @change="flushChange" />
        </el-tooltip>
        <div class="radio_list">
          <el-radio-group v-model="dataCode" size="medium" @change="initLiveData">
            <el-radio-button v-for="data in dataType" :key="data.code" :label="data.code">{{ data.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div v-show="dataCode === 'PRE'" style="margin-left: 20px; margin-top: 3px">
        <el-radio-group v-model="preCheck" @change="initLiveData">
          <el-radio style="font-size: 16px;" label="5min">5min</el-radio>
          <el-radio style="font-size: 16px;" label="30min">30min</el-radio>
          <el-radio style="font-size: 16px;" label="1">1H</el-radio>
          <el-radio style="font-size: 16px;" label="3">3H</el-radio>
          <el-radio style="font-size: 16px;" label="12">12H</el-radio>
        </el-radio-group>
      </div>
    </div>
    <stepsMenu />
    <processDialog :process-visible="processVisible" />
    <stationDialog :visible.sync="stationVisible" :station-id="clickStationId" />
    <!--    底图切换-->
    <div :style="{ right: detectRightShow ? '321px' : '12px' }" class="stationPanel" style="transition: right 0.5s;">
      <div class="radio_list">
        <el-radio-group v-model="mapLayerType" size="medium" @change="mapLayerChange">
          <el-radio-button v-for="type in mapLayerTypes" :key="type.code" :label="type.code">{{ type.label }}
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <!-- 标注显示内容 -->
    <div id="popup" class="ol-popup">
      <div v-html="popupContent" />
    </div>
    <!-- 雷达图微信图时间显示 -->
    <div :style="{ left: detectLeftShow ? '200px' : '15px' }" class="radarsateWrap">
      <div style="user-select: none;">
        <label v-show="stationTime">实况数时间：<span>{{ stationTime }}</span></label>
        <br v-show="radarTime">
        <label v-if="radarTime">雷达图时间：<span>{{ radarTime }}</span></label>
        <br v-show="sateTime">
        <label v-if="sateTime">卫星图时间：<span>{{ sateTime }}</span></label>
        <br v-show="fenglShow">
        <i v-if="fenglShow" class="el-icon-arrow-left" @click="lastPicFengl" />
        <label v-if="fenglShow">回波外推时间：<span>{{ fenglTime }}</span></label>
        <i v-if="fenglShow" class="el-icon-arrow-right" @click="nextPicFengl" />
      </div>
    </div>

    <!-- 雷达图卫星图前后切换 -->
    <div v-show="radarTime || sateTime">
      <span
        :style="{ left: detectLeftShow ? '205px' : '5px' }"
        class="leftRightPic"
        style="background-image: url(static/left.svg);transition: left 0.5s;"
        @click="lastPic"
      />
      <span
        :style="{ right: detectRightShow ? '305px' : '5px' }"
        class="leftRightPic"
        style="background-image: url(static/right.svg); transition: right 0.5s;"
        @click="nextPic"
      />
    </div>
    <!-- 添加阈值列表面板 -->
    <div
      v-if="thresholdPanelVisible && thresholdArr && thresholdArr.length > 0"
      :style="{ right: detectRightShow ? '321px' : '12px', transition: 'right 0.5s' }"
      class="threshold-panel"
    >
      <div class="panel-header">
        <span>当前实况报警</span>
        <el-button
          type="text"
          size="mini"
          class="close-btn"
          @click="thresholdPanelVisible = false"
        >
          <i class="el-icon-close" />
        </el-button>
      </div>
      <div class="panel-content">
        <el-table
          :data="warningStations.slice(0, 10)"
          :show-header="true"
          size="mini"
          style="width: 100%"
          border
          @row-click="handleRowClick"
        >
          <el-table-column
            prop="threshold"
            label="阈值名称"
            align="center"
            min-width="70"
          />
          <el-table-column
            prop="Station_Name"
            label="站名"
            align="center"
            min-width="70"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-tooltip
                :content="scope.row.Station_Name"
                placement="top"
                effect="light"
              >
                <span>{{ formatStationName(scope.row.Station_Name) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="Station_Id_C"
            label="站号"
            align="center"
            min-width="70"
          />
          <el-table-column
            prop="value"
            label="观测值"
            align="center"
            min-width="60"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getWeatherDescByCode as getWeather } from '../utils/index.js'
import stationDialog from './stationDialog'
import { BUS } from '../bus.vue'
import 'ol/ol.css'
import { Map, View } from 'ol'
import Image from 'ol/layer/Image'
import ImageStatic from 'ol/source/ImageStatic'
import Style from 'ol/style/Style'
import Stroke from 'ol/style/Stroke'
import Circle from 'ol/style/Circle'
import Fill from 'ol/style/Fill'
import Icon from 'ol/style/Icon'
import Text from 'ol/style/Text'
import TileLayer from 'ol/layer/Tile'
import Overlay from 'ol/Overlay'
import Feature from 'ol/Feature'
import Point from 'ol/geom/Point'
import Polygon from 'ol/geom/Polygon'
import MultiLineString from 'ol/geom/MultiLineString' // 添加这行
import XYZ from 'ol/source/XYZ'
import SourceVector from 'ol/source/Vector'
import LayerVector from 'ol/layer/Vector'
import LayerGroup from 'ol/layer/Group'
import MousePosition from 'ol/control/MousePosition'
import GeoJSON from 'ol/format/GeoJSON'
import { fromLonLat } from 'ol/proj'
import {
  lastRadar,
  lastRadarTime,
  lastSate,
  lightningData,
  livingData,
  preLivingData,
  getLiveGrid,
  getThresholdData
} from '../api/cimiss'
import settingDialog from './settingDialog'
import { defaults as defaultInteractions } from 'ol/interaction'
import * as turf from '@turf/turf'
import { cityInfo } from '../api/login'
import { getCookie } from '../utils/auth'
import stepsMenu from './stepsMenu.vue'
import processDialog from './processDialog.vue'
const tk = 'ef7e830b6e72f9990997257effbc61c7'
const baseMapDict = {
  nw: {
    vec_w: 'http://10.1.65.144:81/DataServer?T=vec_w&x={x}&y={y}&l={z}',
    img_w: 'http://10.1.65.144:81/DataServer?T=img_w&x={x}&y={y}&l={z}',
    ter_w: 'http://10.1.65.144:81/DataServer?T=ter_w&x={x}&y={y}&l={z}'
  },
  net: {
    vec_w:
            'http://t4.tianditu.com/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    img_w:
            'http://t4.tianditu.com/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    ter_w:
            'http://t4.tianditu.com/DataServer?T=ter_w&x={x}&y={y}&l={z}&tk=' +
            tk
  }
}
const annotationDict = {
  nw: {
    vec_w: 'http://10.1.65.144:81/DataServer?T=cva_w&x={x}&y={y}&l={z}',
    img_w: 'http://10.1.65.144:81/DataServer?T=cia_w&x={x}&y={y}&l={z}',
    ter_w: 'http://10.1.65.144:81/DataServer?T=cta_w&x={x}&y={y}&l={z}'
  },
  net: {
    vec_w:
            'http://t4.tianditu.com/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    img_w:
            'http://t4.tianditu.com/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=' +
            tk,
    ter_w:
            'http://t4.tianditu.com/DataServer?T=cta_w&x={x}&y={y}&l={z}&tk=' +
            tk
  }
}

const attachDatas = [
  // 风标
  {
    x: 0,
    y: 0,
    element: ['WIN_D_INST_Max', 'WIN_S_Inst_Max'],
    formatter: function(element) {
      return {
        radDirect: ((element[0] * Math.PI) / 180).toFixed(2),
        level: Math.round(element[1] / 2)
      }
    },
    value: 'ddff',
    dataCode: ['SURF_CHN_MUL_HOR']
  },
  {
    x: 0,
    y: 0,
    element: ['WIN_D_Avg_2mi', 'WIN_S_Avg_2mi'],
    formatter: function(element) {
      return {
        radDirect: ((element[0] * Math.PI) / 180).toFixed(2),
        level: Math.round(element[1] / 2)
      }
    },
    value: 'ddff',
    dataCode: ['SURF_CHN_MUL_MIN']
  },
  {
    x: -0.6,
    y: -0.6,
    color: '#e74c3c',
    element: 'TEM',
    formatter: function(value) {
      if (value > 99999) {
        return ''
      }
      return value
    },
    value: 'TT',
    dataCode: ['SURF_CHN_MUL_MIN', 'SURF_CHN_MUL_HOR']
  },
  // 现在天气现象
  {
    x: 0,
    y: 0,
    element: 'WEP_Now',
    value: 'WW',
    formatter: function(value) {
      return value
    },
    dataCode: ['SURF_CHN_MUL_HOR']
  },
  // 站点
  {
    x: 0,
    y: 0,
    value: 'X',
    dataCode: [
      'SURF_CHN_MUL_MIN',
      'SURF_CHN_MUL_HOR',
      'SURF_WEA_CHN_PRE_MIN'
    ]
  },
  // 能见度(单位：公里）
  {
    x: -1,
    y: 0.3,
    color: '#7e4d05f5',
    element: 'VIS_Min',
    formatter: function(value) {
      if (value > 99999) {
        return ''
      }
      return value
    },
    value: 'VV',
    dataCode: ['SURF_CHN_MUL_HOR']
  },
  {
    x: -1,
    y: 0.3,
    color: '#7e4d05f5',
    element: 'VIS_HOR_1MI',
    formatter: function(value) {
      if (value > 99999) {
        return ''
      }
      return value
    },
    value: 'VV',
    dataCode: ['SURF_CHN_MUL_MIN']
  },
  // 相对湿度
  {
    x: -1,
    y: 1,
    color: '#a0f176',
    element: 'RHU',
    formatter: function(value) {
      if (value > 99999) {
        return ''
      }
      return value
    },
    value: 'RH',
    dataCode: ['SURF_CHN_MUL_MIN', 'SURF_CHN_MUL_HOR']
  },
  // 降水量
  {
    x: 0.6,
    y: 0.6,
    color: '#ff0000',
    element: 'PRE_1h',
    formatter: function(value) {
      if (value > 99999) {
        return ''
      } else if (value === 999999) {
        return '0.0'
      } else if (value === '0') {
        return ''
      }
      return value
    },
    value: 'RR',
    dataCode: ['SURF_CHN_MUL_HOR']
  },
  {
    x: 0.4,
    y: 0.4,
    color: '#000',
    element: 'PRE',
    formatter: function(value) {
      if (value > 99999) {
        return ''
      } else if (value === 999999) {
        return '0.0'
      } else if (value === '0') {
        return ''
      }
      return value
    },
    value: 'RR',
    dataCode: ['SURF_CHN_MUL_MIN']
  },
  {
    x: 0.3,
    y: 0.3,
    color: '#000',
    element: 'SUM_PRE',
    formatter: function(value) {
      return value
    },
    value: 'MIN_RR',
    dataCode: ['PRE']
  },
  {
    x: 0.3,
    y: 0.3,
    color: '#000',
    element: 'SUM_PRE_1H',
    formatter: function(value) {
      return value
    },
    value: 'SUM_RR',
    dataCode: ['PRE']
  }
]

export default {
  components: {
    stationDialog,
    settingDialog,
    stepsMenu,
    processDialog
  },
  // name: "baseMap",
  data() {
    return {
      preCheck: '1',
      flushTimer: null,
      dataType: [
        { label: '小时数据', code: 'SURF_CHN_MUL_HOR' },
        { label: '分钟数据', code: 'SURF_CHN_MUL_MIN' },
        { label: '降水', code: 'PRE' }
      ],
      radarTime: false,
      fenglTime: false,
      stationTime: false,
      sateTime: false,
      radarShow: 'QR',
      fenglShow: true,
      sateShow: 'fy01',
      TTShow: false,
      ddffShow: true,
      RRShow: true,
      VVShow: true,
      RHShow: false,
      WWShow: true,
      XShow: true,
      showLightning: true,
      detectLeftShow: true,
      detectRightShow: true,
      map: undefined,
      stationVisible: false,
      clickStationId: '',
      mapLayerType: 'img_w', // 地图类型
      mapType: 'nw', // 地图类型
      mapLayerTypes: [
        { label: '矢量', code: 'vec_w' },
        { label: '影像', code: 'img_w' },
        { label: '地形', code: 'ter_w' }
      ],
      mapTypes: [
        { label: '内网', code: 'nw' },
        { label: '外网', code: 'net' }
      ],
      // 实况阈值数组
      thresholdArr: [],
      // 所有实况阈值数据缓存，key��阈值主键，value为阈值对象
      thresholdCache: {},
      // 所有预报阈值数据缓存，key为阈值主键，value为阈��对象
      forecastThresholdCache: {},
      boundaryVector1: null, // 边界
      boundaryVector0: null, // 内部旗县边界
      boundaryVector2: null, // 警戒区
      boundaryVector3: null, // 监视区
      livingFeatureCache: {},
      baseChoose: '',
      baseChooseOptions: [],
      time: '',
      title: '地面站',
      dialogVisible: false,
      popupContent: '', // 标注内容
      popupContainer: '', // 标注节点
      chartTitle: '', // 弹框标题
      stationLayer: '', // 站点图层
      lightningLayer: '', // 闪电图层
      warningLayer: '',
      radarLayerQR: new Image({ opacity: 0.6 }), // 基本反射率图层
      radarLayerCR: new Image({ opacity: 0.6 }), // 组合反射率图层
      fenglLayerCR: new Image({ opacity: 0.6 }), // 风雷基本反射率图层
      fy4a01Layer: new Image({ opacity: 0.6 }), // 云图系列图层
      fy4a09Layer: new Image({ opacity: 0.6 }),
      fy4a12Layer: new Image({ opacity: 0.6 }),
      b03Layer: new Image({ opacity: 0.6 }),
      b08Layer: new Image({ opacity: 0.6 }),
      b13Layer: new Image({ opacity: 0.6 }),
      liveGridLayer: null,
      liveGridElement: null,
      liveGridTime: null,
      radarCRArr: [], // 组合反射率数据
      fenglArr: [],
      radarQRArr: [], // 基本反射率数据
      fy4a01Arr: [], // 云图数组
      fy4a09Arr: [],
      fy4a12Arr: [],
      b03Arr: [],
      b08Arr: [],
      b13Arr: [],
      indexfy01: -1,
      indexfy09: -1,
      indexfy12: -1,
      indexb13: -1,
      indexb03: -1,
      indexb08: -1,
      btnLeftOpen: true,
      indexCR: -1,
      indexFengl: 0,
      indexQR: -1,
      radarExtent: [73.0, 14.76, 135.0, 56.8],
      fenglExtent: [70.0, 15.0, 137.0, 55.0],
      sateExtent: [70.0, 17.0, 140.0, 55.0],
      stationTableLayerTilte: '', // 图表标题
      // windLayer: "", //风流场图层
      chartVisible: false,
      begTime: '',
      endTime: '',
      chart: null,
      livingTime: new Date(),
      livingData: [],
      lightningData: [],
      warningStations: [],
      lightningHours: 1,
      cityInfo: '',
      dataCode: 'SURF_CHN_MUL_HOR',
      _plot: null,
      autoFlush: false,
      processVisible: false,
      warningBlinkTimer: null,
      warningVisible: true,
      thresholdPanelVisible: true,
      elementLabels: {
        'PRE': '分钟降水量',
        'PRE_1h': '小时累计降水',
        'TEM': '分钟气温',
        'WIN_S_INST': '瞬时风速',
        'WIN_S_Avg_2mi': '2分钟风速',
        'VIS_HOR_1MI': '1分钟���见度',
        'RHU': '相对湿度'
      },
      highlightLayer: null,
      warningAudio: null,
      hasPlayedWarning: false, // 添加标记，记录是否已经播放过警告音
      audioContext: null,
      warningBuffer: null,
      audioInitialized: false
    }
  },
  computed: {

  },
  created() {
    // this.reloadThresholdData();
  },
  mounted() {
    // 初始化 Web Audio API
    this.initAudio()

    // 创建音频对象并设置属性
    this.warningAudio = new Audio(require('@/assets/warning.wav'))
    this.warningAudio.muted = true // 初始设置为静音
    this.warningAudio.autoplay = true // 设置自动播放
    this.warningAudio.load()

    // 在页面加载后立即播放一次静音的音频
    // 这样可以解除浏览器对后续播放的限制
    this.warningAudio.play().then(() => {
      this.warningAudio.muted = false // 取消静音
    }).catch(e => console.log('Initial muted play failed:', e))

    BUS.$on('radarLayerChange', val => {
      this.radarChanged(val)
    })
    BUS.$on('fenglChange', val => {
      this.switchFengl(val)
    })
    // BUS.$on("changeCLDAS", (element,val) => {
    //     this.changeCLDAS(element,val);
    // });
    BUS.$on('settingVisible', val => {
      this.settingVisible = val
    })
    BUS.$on('stationVisible', val => {
      this.stationVisible = val
    })
    BUS.$on('sateLayerChange', val => {
      this.sateChanged(val)
    })
    BUS.$on('detectLeftShow', val => {
      this.detectLeftShow = val
    })
    BUS.$on('detectRightShow', val => {
      this.detectRightShow = val
    })
    BUS.$on('changeStationEle', (type, val) => {
      this.changeStationEle(type, val)
    })
    BUS.$on('lightningChange', (visible, val) => {
      this.changeLightningEle(visible, val)
    })
    BUS.$on('openProcess', val => {
      this.changeProcessVisible()
    })
    BUS.$on('processDialogClose', val => {
      this.changeProcessVisible()
    })
    this.initMap()
    this.getCityInfo()
    this.initLiveData()
    this.initLightning()
    this.initRadarData()
    // this.getWindJsonFile();
    this.initSateData()
    this.initFengl()
    this.initBoundaryVector()
    this.thresholdData()
  },
  beforeDestroy() {
    clearInterval(this.flushTimer)
    this.flushTimer = null
    if (this.warningBlinkTimer) {
      clearInterval(this.warningBlinkTimer)
      this.warningBlinkTimer = null
    }
    if (this.highlightLayer) {
      this.map.removeLayer(this.highlightLayer)
    }
    if (this.warningAudio) {
      this.warningAudio.pause()
      this.warningAudio = null
    }
    if (this.audioContext) {
      this.audioContext.close()
    }
  },
  methods: {
    changeProcessVisible() {
      this.processVisible = this.processVisible !== true
    },
    getWeatherDescByCode(code) {
      return getWeather(code)
    },
    getLivingData() {
      const _this = this
      const params = {
        dataCode: _this.dataCode,
        times: _this.livingTime
      }
      livingData(params)
        .then(res => {
          if (res.code === 200) {
            _this.livingData = res.data['DS']
            _this.stationTime = _this.getFormatTime(
              _this.livingData[0]['Datetime']
            )
            _this.reloadLiveData()
          } else {
          }
        })
        .catch(err => { })
    },

    thresholdData() {
      const _this = this
      getThresholdData().then(res => {
        if (res.code === 200) {
          // 检查是否有超阈值数据
          let hasWarningData = false
          for (const key in res.data) {
            if (res.data[key].data && res.data[key].data.length > 0) {
              hasWarningData = true
              break
            }
          }

          // 如果有超阈值数据，尝试播放警告音
          if (hasWarningData) {
            _this.playWarningSound()
          }

          // 原有代码继续执行...
          _this.thresholdArr = Object.keys(res.data).map(key => {
            return {
              ...res.data[key],
              key: key
            }
          })
          _this.thresholdArr.forEach(item => {
            if (item.type.includes('小于')) {
              item.data.sort((a, b) => a.value - b.value)
            } else {
              item.data.sort((a, b) => b.value - a.value)
            }
          })

          // 创建坐标计数器对象
          const coordinateCount = {}

          // 清除之前的定时器
          if (_this.warningBlinkTimer) {
            clearInterval(_this.warningBlinkTimer)
          }

          // 遍历原始数据添加警告图标
          for (const key in res.data) {
            const th = JSON.parse(JSON.stringify(res.data[key]))
            th.data = ''
            res.data[key].data.forEach(t => {
              const latlon = [parseFloat(t.Lon), parseFloat(t.Lat)]
              const coordKey = latlon.join(',')

              // 初始化或增加该坐标的计数
              coordinateCount[coordKey] = (coordinateCount[coordKey] || 0) + 1

              // 计算偏移量
              const angle = (coordinateCount[coordKey] - 1) * (Math.PI / 4) // 每个点间隔45度，顺时针变化
              const radius = 0.07
              const offsetX = radius * Math.cos(angle) // 经度偏移
              const offsetY = radius * Math.sin(angle) // 纬度偏移

              // 添加偏移量到坐标
              const offsetLatlon = [latlon[0] + offsetX, latlon[1] + offsetY]
              const coor = fromLonLat(offsetLatlon)

              const feature = new Feature({
                geometry: new Point(coor)
              })

              // 创建闪烁样式
              const createBlinkStyle = (visible) => {
                const warningColor = res.data[key].color || 'red'
                return new Style({
                  image: new Icon({
                    src: require('@/assets/images/warning_' + warningColor + '.png'),
                    scale: visible ? 0.4 : 0,
                    anchor: [0, 1],
                    anchorXUnits: 'fraction',
                    anchorYUnits: 'fraction'
                  })
                })
              }

              feature.setStyle(createBlinkStyle(true))
              feature.threshold = th
              feature.attachData = t
              feature.name = 'warning'
              feature.setId(t.stationIdC)
              _this.warningLayer.getSource().addFeature(feature)
            })
          }

          // 计算每个阈值项需要取出的数据数量
          const totalItems = 10
          const itemsPerThreshold = Math.floor(totalItems / _this.thresholdArr.length)
          const remainingItems = totalItems % _this.thresholdArr.length

          _this.warningStations = []
          _this.thresholdArr.forEach((item, index) => {
            const count = itemsPerThreshold + (index < remainingItems ? 1 : 0)
            item.data.slice(0, count).forEach(station => {
              station.threshold = item.name
              _this.warningStations.push(station)
            })
          })

          // 重新启动闪烁定时器
          _this.warningVisible = true
          _this.warningBlinkTimer = setInterval(() => {
            _this.warningVisible = !_this.warningVisible
            _this.warningLayer.getSource().getFeatures().forEach(f => {
              if (f.name === 'warning') {
                const th = f.threshold
                const warningColor = th.color || 'red'
                f.setStyle(new Style({
                  image: new Icon({
                    src: require('@/assets/images/warning_' + warningColor + '.png'),
                    scale: _this.warningVisible ? 0.4 : 0,
                    anchor: [0, 1],
                    anchorXUnits: 'fraction',
                    anchorYUnits: 'fraction',
                    offsetX: 15,
                    offsetY: -15
                  })
                }))
              }
            })
          }, 500)
        }
      })
    },
    getLightningData() {
      const _this = this
      const params = {
        hours: _this.lightningHours,
        time: _this.livingTime
      }
      lightningData(params)
        .then(res => {
          if (res.code === 200) {
            _this.lightningData = res.data['DS']
            _this.reloadLightning()
          } else {
          }
        })
        .catch(err => { })
    },

    getPreLivingData() {
      const _this = this
      const params = {
        preType: _this.preCheck,
        times: _this.livingTime
      }
      preLivingData(params)
        .then(res => {
          if (res.code === 200) {
            _this.livingData = res.data['DS']
            _this.reloadLiveData()
          } else {
          }
        })
        .catch(err => { })
    },
    changeDataCode() { },
    getWindJsonFile() {
      const _this = this
      fetch('https://sakitam-fdd.github.io/wind-layer/data/wind.json')
        .then(res => res.json())
        .then(res => {
          const windLayer = new WindLayer(res, {
            windOptions: {
              // colorScale: scale,
              minVelocity: 3,
              maxVelocity: 3,
              velocityScale: 1 / 10,
              paths: 500,
              // eslint-disable-next-line no-unused-lets
              colorScale: [
                'rgb(36,104, 180)',
                'rgb(60,157, 194)',
                'rgb(128,205,193 )',
                'rgb(151,218,168 )',
                'rgb(198,231,181)',
                'rgb(238,247,217)',
                'rgb(255,238,159)',
                'rgb(252,217,125)',
                'rgb(255,182,100)',
                'rgb(252,150,75)',
                'rgb(250,112,52)',
                'rgb(245,64,32)',
                'rgb(237,45,28)',
                'rgb(220,24,32)',
                'rgb(180,0,35)'
              ],
              lineWidth: 2,
              // colorScale: scale,
              generateParticleOption: true
            }
            // map: map,
            // projection: 'EPSG:4326'
          })

          console.log(map, windLayer)

          _this.map.addLayer(windLayer)
        })
    },

    // 底图切换
    mapLayerChange() {
      const _this = this
      const layers = _this.map.getLayers()
      for (let i = 0; i < layers.getLength(); i++) {
        if (layers.item(i).getClassName() === 'baseMap') {
          _this.map
            .getLayers()
            .item(i)
            .setSource(
              new XYZ({
                url:
                                    baseMapDict[this.mapType][
                                      _this.mapLayerType
                                    ],
                projection: 'EPSG:3857'
              })
            )
        } else if (layers.item(i).getClassName() === 'annotation') {
          _this.map
            .getLayers()
            .item(i)
            .setSource(
              new XYZ({
                url:
                                    annotationDict[this.mapType][
                                      _this.mapLayerType
                                    ],
                projection: 'EPSG:3857'
              })
            )
        }
      }
    },

    getCityInfo() {
      const _this = this

      cityInfo()
        .then(res => {
          if (res.code === 200) {
            BUS.$emit('setCityInfo', res.data['cityInfo']),
            (_this.cityInfo = res.data['cityInfo'])
            _this.map
              .getView()
              .setCenter(
                fromLonLat(_this.cityInfo.LatLon.split(',')
                ))
          }
        })
        .catch(err => { })
    },

    getGeojsonFile(file) {
      const xmlobj = new XMLHttpRequest()

      xmlobj.overrideMimeType('application/json')

      xmlobj.open('GET', file, true)

      xmlobj.onreadystatechange = function() {
        if (xmlobj.readyState == 4 && xmlobj.status == '200') {
          // Required use of an anonymous callback as .open will NOT return a value but simply returns undefined in asynchronous mode
          callback(xmlobj.responseText)
        }
      }

      xmlobj.send(null)
    },

    initBoundaryVector() {
      const _this = this
      const geojson = 'static/json/' + getCookie('areaCode') + '.json'

      const source0 = new SourceVector({
        projection: 'EPSG:4326',
        url: 'static/json/' + getCookie('areaCode') + '1.json',
        format: new GeoJSON()
      })
      _this.boundaryVector0 = new LayerVector({
        source: source0,
        style: new Style({
          stroke: new Stroke({
            color: '#300dd7',
            width: 2.0
          })
        })
      })
      _this.map.addLayer(_this.boundaryVector0)

      const source1 = new SourceVector({
        projection: 'EPSG:4326',
        url: geojson,
        format: new GeoJSON()
      })
      _this.boundaryVector1 = new LayerVector({
        source: source1,
        style: new Style({
          stroke: new Stroke({
            color: '#300dd7',
            width: 2.5
          })
        })
      })
      _this.map.addLayer(_this.boundaryVector1)

      fetch(geojson)
        .then(function(response) {
          return response.json()
        })
        .then(function(json) {
          const format = new GeoJSON()
          const features = format.readFeatures(json)
          const feature = features[0]
          const turfLine = format.writeFeatureObject(feature)

          const buf2 = turf.buffer(turfLine, 25)
          const f2 = format.readFeature(buf2)
          f2.getGeometry().transform('EPSG:4326', 'EPSG:3857')
          const source2 = new SourceVector({
            features: [f2]
          })

          _this.boundaryVector2 = new LayerVector({
            source: source2,
            style: new Style({
              stroke: new Stroke({
                color: '#c0392b',
                width: 2.5
              })
            })
          })
          _this.map.addLayer(_this.boundaryVector2)

          const buf3 = turf.buffer(turfLine, 50)
          const f3 = format.readFeature(buf3)
          f3.getGeometry().transform('EPSG:4326', 'EPSG:3857')
          const source3 = new SourceVector({
            features: [f3]
          })

          _this.boundaryVector3 = new LayerVector({
            source: source3,
            style: new Style({
              stroke: new Stroke({
                color: '#f39c12',
                width: 2.5
              })
            })
          })
          _this.map.addLayer(_this.boundaryVector3)
        })
    },

    initMap() {
      const _this = this

      const mousePositionControl = new MousePosition({
        coordinateFormat: function(coor) {
          return (
            '经度：' +
                        coor[0].toFixed(3) +
                        '&nbsp;&nbsp;&nbsp;&nbsp;纬度：' +
                        coor[1].toFixed(3)
          )
        },
        projection: 'EPSG:4326',
        // comment the following two lines to have the mouse position
        // be placed within the map.
        className: 'custom-mouse-position',
        target: document.getElementById('mouse-position'),
        undefinedHTML: '&nbsp;&nbsp;&nbsp;&nbsp;'
      })
      // 每个要素��在单独的图层，根据要素个数确定图层个数
      const stationLayers = []
      for (let i = 0; i < attachDatas.length; i++) {
        const layerC = new LayerVector({
          source: new SourceVector({})
        })
        layerC.setVisible(_this[`${attachDatas[i].value + 'Show'}`])
        layerC.setProperties({ type: attachDatas[i].value })
        stationLayers.push(layerC)
      }
      // 站点图层
      _this.stationLayer = new LayerGroup({
        layers: stationLayers
      })

      const layerL = new LayerVector({
        source: new SourceVector({})
      })

      _this.lightningLayer = new LayerGroup({
        layers: [layerL]
      })
      // 报警图层
      _this.warningLayer = new LayerVector({
        source: new SourceVector({})
      })

      _this.liveGridLayer = new LayerGroup({
        layers: [
          new LayerVector({
            source: new SourceVector({}),
            style: function(feature) {
              return new Style({
                fill: new Fill({
                  color: feature.getProperties().color + '6C'
                })
              })
            }
          }),
          new LayerVector({
            source: new SourceVector({}),
            style: function(feature) {
              const props = feature.getProperties()
              if (props.element === 'WIN') {
                const windLevel = Math.round(props.levelGridData)
                const windDirect = props.directGridData
                // TODO 风级计算
                const windRad = (windDirect * Math.PI / 180).toFixed(2)
                return Style({
                  image: Icon({
                    src: 'images/windSymbol/wind' + windLevel + '.svg',
                    rotation: windRad,
                    scale: 0.4,
                    anchor: [60, -25],
                    anchorXUnits: 'pixels',
                    anchorYUnits: 'pixels'
                  })
                })
              } else {
                return new Style({
                  text: new Text({
                    font: '15px Microsoft YaHei',
                    text: props.gridValue.toFixed(1),
                    color: '#000',
                    placement: 'point',
                    offsetY: 25
                  })
                })
              }
            }
          })
        ]
      })
      // 标注图层
      _this.popupContainer = document.getElementById('popup')
      window.popupOverlay = new Overlay({
        element: _this.popupContainer,
        autoPan: true,
        autoPanAnimation: {
          duration: 250
        }
      })
      // 外网天地图
      // let proj = getProjection("EPSG:3857");
      // let projectionExtent = proj.getExtent();
      // let size = getWidth(projectionExtent) / 256;
      // let resolutions = new Array(14);
      // let matrixIds = new Array(14);
      // for (let z = 0; z < 14; ++z) {
      //   // generate resolutions and matrixIds arrays for this WMTS
      //   resolutions[z] = size / Math.pow(2, z);
      //   matrixIds[z] = z;
      // }
      _this.map = new Map({
        layers: [
          // 底图层
          new TileLayer({
            opacity: 1,
            className: 'baseMap',
            source: new XYZ({
              url: baseMapDict[this.mapType][_this.mapLayerType],
              projection: 'EPSG:3857'
            })
          }),
          new TileLayer({
            opacity: 1,
            className: 'annotation',
            source: new XYZ({
              url: annotationDict[this.mapType][_this.mapLayerType],
              projection: 'EPSG:3857'
            })
          }),
          _this.stationLayer,
          _this.lightningLayer,
          _this.radarLayerQR,
          _this.radarLayerCR,
          // _this.fenglLayerCR,
          _this.fy4a01Layer,
          _this.fy4a09Layer,
          _this.fy4a12Layer,
          _this.b03Layer,
          _this.b08Layer,
          _this.b13Layer,
          // 将 warningLayer 放在���上层
          _this.warningLayer
        ],
        overlays: [window.popupOverlay],
        target: 'map',
        view: new View({
          center: fromLonLat([106.8, 41.24]),
          zoom: 8,
          projection: 'EPSG:3857'
        }),
        controls: defaultInteractions().extend([mousePositionControl])
      })
      _this.map.getView().on('change:resolution', _this.reloadGridPoint)
      _this.map.addEventListener('pointermove', function(e) {
        if (true) {
          const coordinate = e.coordinate
          popupOverlay.setPosition(undefined)
          _this.map.forEachFeatureAtPixel(e.pixel, function(feature, layer) {
            if (feature) {
              if (feature.name === 'station') {
                const title = feature.refData.Station_Name
                  ? '<p style="font-size:16px;border-bottom: 1px solid #e6a23c;margin-bottom:8px;padding-bottom:6px"><strong>' +
                                    feature.refData.Station_Name +
                                    '</strong>(' +
                                    feature.refData.Station_Id_C +
                                    ')</p>'
                  : '未知台站'
                const pre =
                                    feature.refData.PRE &&
                                        feature.refData.PRE != 999998 &&
                                        feature.refData.PRE != 999999
                                      ? '<p><strong>分钟降水量：</strong>' +
                                        feature.refData.PRE +
                                        'mm</p>'
                                      : ''
                const pre1 =
                                    feature.refData.PRE_1h &&
                                        feature.refData.PRE_1h != 999998 &&
                                        feature.refData.PRE_1h != 999999
                                      ? '<p><strong>1h降水量：</strong>' +
                                        feature.refData.PRE_1h +
                                        'mm</p>'
                                      : ''
                const pre3 =
                                    feature.refData.PRE_3h &&
                                        feature.refData.PRE_3h != 0 &&
                                        feature.refData.PRE_12h != 999999
                                      ? '<p><strong>3h降水量：</strong>' +
                                        feature.refData.PRE_3h +
                                        'mm</p>'
                                      : ''
                const pre6 =
                                    feature.refData.PRE_6h &&
                                        feature.refData.PRE_6h != 0 &&
                                        feature.refData.PRE_12h != 999999
                                      ? '<p><strong>6h降水量：</strong>' +
                                        feature.refData.PRE_6h +
                                        'mm</p>'
                                      : ''
                const pre12 =
                                    feature.refData.PRE_12h &&
                                        feature.refData.PRE_12h != 0 &&
                                        feature.refData.PRE_12h != 999999
                                      ? '<p><strong>12h降水量：</strong>' +
                                        feature.refData.PRE_12h +
                                        'mm</p>'
                                      : ''
                const pre24 =
                                    feature.refData.PRE_24h &&
                                        feature.refData.PRE_24h != 0 &&
                                        feature.refData.PRE_12h != 999999
                                      ? '<p><strong>24h降水量：</strong>' +
                                        feature.refData.PRE_24h +
                                        'mm</p>'
                                      : ''
                const tem =
                                    feature.refData.TEM &&
                                        feature.refData.TEM != 999999
                                      ? '<p><strong>气温：</strong>' +
                                        feature.refData.TEM +
                                        '℃</p>'
                                      : ''
                const wind_inst =
                                    feature.refData.WIN_S_Inst_Max &&
                                        feature.refData.WIN_S_Inst_Max != 999999
                                      ? '<p><strong>极大风速：</strong>' +
                                        feature.refData.WIN_S_Inst_Max +
                                        'm/s</p>'
                                      : ''
                const wind =
                                    feature.refData.WIN_S_Avg_2mi &&
                                        feature.refData.WIN_S_Avg_2mi != 999999
                                      ? '<p><strong>2分钟风速：</strong>' +
                                        feature.refData.WIN_S_Avg_2mi +
                                        'm/s</p>'
                                      : ''
                const vis_min =
                                    feature.refData.VIS_Min &&
                                        feature.refData.VIS_Min != 999999
                                      ? '<p><strong>最小能见度：</strong>' +
                                        feature.refData.VIS_Min +
                                        'm</p>'
                                      : ''
                const vis =
                                    feature.refData.VIS_HOR_1MI &&
                                        feature.refData.VIS_HOR_1MI != 999999
                                      ? '<p><strong>1分钟能见度：</strong>' +
                                        feature.refData.VIS_HOR_1MI +
                                        'm</p>'
                                      : ''
                const rh =
                                    feature.refData.RHU &&
                                        feature.refData.RHU != 999999
                                      ? '<p><strong>相对湿度：</strong>' +
                                        feature.refData.RHU +
                                        '%</p>'
                                      : ''
                const prs =
                                    feature.refData.PRS &&
                                        feature.refData.PRS != 999999
                                      ? '<p><strong>本站气压：</strong>' +
                                        feature.refData.PRS +
                                        'hPa</p>'
                                      : ''
                const CLO_Cov =
                                    feature.refData.CLO_Cov &&
                                        feature.refData.CLO_Cov < 99999
                                      ? '<p><strong>云量���</strong>' +
                                        feature.refData.PRS +
                                        '%</p>'
                                      : ''
                const CLO_Height =
                                    feature.refData.CLO_Height_LoM &&
                                        feature.refData.CLO_Height_LoM < 99999
                                      ? '<p><strong>云底高度：</strong>' +
                                        feature.refData.CLO_Height_LoM +
                                        'm</p>'
                                      : ''
                const weather =
                                    _this.getWeatherDescByCode(
                                      feature.refData.WEP_Now
                                    ) &&
                                        _this.getWeatherDescByCode(
                                          feature.refData.WEP_Now
                                        ) != undefined
                                      ? '<p><strong>天气现象：</strong>' +
                                        _this.getWeatherDescByCode(
                                          feature.refData.WEP_Now
                                        ) +
                                        '</p>'
                                      : ''
                const time =
                                    '<p><strong>时间：</strong>' +
                                    _this.getFormatTime(
                                      feature.refData.Datetime
                                    ) +
                                    '</p>'
                _this.popupContent =
                                    title +
                                    '<p><strong>所属盟市：</strong>' +
                                    (feature.refData.City || '-') +
                                    '</p>' +
                                    '<p><strong>所属旗县：</strong>' +
                                    (feature.refData.Cnty || '-') +
                                    '</p>' +
                                    pre +
                                    pre1 +
                                    pre3 +
                                    pre6 +
                                    pre12 +
                                    pre24 +
                                    tem +
                                    wind_inst +
                                    wind +
                                    vis_min +
                                    vis +
                                    rh +
                                    prs +
                                    weather +
                                    CLO_Cov +
                                    CLO_Height +
                                    time
                popupOverlay.setPosition(coordinate)
              } else if (feature.name === 'lightning') {
                const prov = feature.refData.Lit_Prov
                  ? '<p><strong>出现省份：</strong>' +
                                    feature.refData.Lit_Prov +
                                    '</p>'
                  : ''
                const city = feature.refData.Lit_City
                  ? '<p><strong>出现省份：</strong>' +
                                    feature.refData.Lit_City +
                                    '</p>'
                  : ''
                const cnty = feature.refData.Lit_Cnty
                  ? '<p><strong>出现省份：</strong>' +
                                    feature.refData.Lit_Cnty +
                                    '</p>'
                  : ''
                _this.popupContent =
                                    '<p style="font-size:16px;border-bottom: 1px solid #e6a23c;margin-bottom:8px;padding-bottom:6px"><strong>雷电</strong></p>' +
                                    prov +
                                    city +
                                    cnty +
                                    '<p><strong>电流强度：</strong>' +
                                    feature.refData.Lit_Current +
                                    'kA</p>' +
                                    '<p><strong>出��时间：</strong>' +
                                    (_this.getFormatTime(
                                      feature.refData.Datetime
                                    ) || '-') +
                                    '</p>'
                popupOverlay.setPosition(coordinate)
              } else if (feature.name === 'alert') {
                _this.popupContent =
                                    '<p style="font-size:16px;border-bottom: 1px solid #ddd;"><strong>' +
                                    feature.attachData.type +
                                    '</strong></p>' +
                                    '<p><strong>发布单位：</strong>' +
                                    (feature.attachData.sender || '-') +
                                    '</p>' +
                                    '<p><strong>预警等级：</strong>' +
                                    feature.attachData.type +
                                    '</p>' +
                                    '<p><strong>预警内容：</strong>' +
                                    (feature.attachData.description || '-') +
                                    '</span></p>' +
                                    '<p><strong>签发时间：</strong>' +
                                    (feature.attachData.Datetime || '-') +
                                    '</p>'
                popupOverlay.setPosition(coordinate)
              } else if (feature.name === 'warning') {
                const t = feature.attachData
                const th = feature.threshold
                _this.popupContent =
                                    '<p style="font-size:16px;border-bottom: 1px solid #ddd;"><strong>超阈值报警</strong></p>' +
                                    '<p><strong>站点：</strong>' +
                                    t.Station_Name + '(' + t.Station_Id_C + ')' +
                                    '</p>' +
                                    '<p><strong>所属盟市：</strong>' +
                                    (t.City || '-') +
                                    '</p>' +
                                    '<p><strong>所属旗县：</strong>' +
                                    (t.Cnty || '-') +
                                    '</p>' +
                                    '<p><strong>阈值名称：</strong>' +
                                    (th.name || '-') +
                                    '</p>' +
                                    '<p><strong>阈值：</strong><span>' +
                                    (th.value || '-') +
                                    '<p><strong>实况值：</strong><span>' +
                                    (t.value || '-') +
                                    '</span></p>' +
                                    '<p><strong>报警时间：</strong>' +
                                    ((t.Datetime &&
                                    _this.getFormatTime(t.Datetime.replace('T', ' ')
                                    ))) ||
                                        '-' +
                                    '</p>'
                popupOverlay.setPosition(coordinate)
              } else if (feature.name === 'PRE_warning') {
                const c = feature.attachData
                _this.popupContent =
                                    '<p style="font-size:16px;border-bottom: 1px solid #ddd;"><strong>' +
                                    (c.Station_Name || '-') +
                                    '</strong></p>' +
                                    '<p><strong>时间：</strong>' +
                                    (c.Datetime || '-') +
                                    '</p>' +
                                    '<p><strong>站号：</strong>' +
                                    (c.Station_Id_C || '-') +
                                    '</p>' +
                                    '<p><strong>经度��</strong>' +
                                    (c.Lon || '-') +
                                    '</p>' +
                                    '<p><strong>纬度：</strong>' +
                                    (c.Lat || '-') +
                                    '</p>' +
                                    '<p><strong>过去1小时降水：</strong>' +
                                    (!c.PRE_1h || c.PRE_1h > 9999
                                      ? '-'
                                      : c.PRE_1h) +
                                    '</p>' +
                                    '<p><strong>过去3小时降水：</strong>' +
                                    (!c.PRE_3h || c.PRE_3h > 9999
                                      ? '-'
                                      : c.PRE_3h) +
                                    '</p>' +
                                    '<p><strong>过去6小时降水：</strong>' +
                                    (!c.PRE_6h || c.PRE_6h > 9999
                                      ? '-'
                                      : c.PRE_6h) +
                                    '</p>' +
                                    '<p><strong>过去12小时降水：</strong>' +
                                    (!c.PRE_12h || c.PRE_12h > 9999
                                      ? '-'
                                      : c.PRE_12h) +
                                    '</p>' +
                                    '<p><strong>过去24小时降水：</strong>' +
                                    (!c.PRE_24h || c.PRE_24h > 9999
                                      ? '-'
                                      : c.PRE_24h) +
                                    '</p>'
                popupOverlay.setPosition(coordinate)
              }
            }
          })
        }
      })
    },
    getFormatTime(time) {
      const temp = new Date(time)
      // 添加 8 小时转换为北京时
      temp.setTime(temp.getTime() + 8 * 60 * 60 * 1000)
      return temp.getFullYear() + '-' +
                (temp.getMonth() + 1).toString().padStart(2, '0') + '-' +
                temp.getDate().toString().padStart(2, '0') + ' ' +
                temp.getHours().toString().padStart(2, '0') + ':' +
                temp.getMinutes().toString().padStart(2, '0')
    },
    lastPicFengl() {
      const _this = this
      if (_this.fenglTime && _this.indexFengl > 0) {
        _this.indexFengl--
        _this.fenglTime = _this.fenglArr[_this.indexFengl].time
      } else {
        _this.$message.info('风雷图已经是第一张了！')
      }
      _this.changeFengl()
    },
    nextPicFengl() {
      const _this = this
      if (_this.fenglTime && _this.indexFengl < _this.fenglArr.length - 1) {
        _this.indexFengl++
        _this.fenglTime = _this.fenglArr[_this.indexFengl].time
      } else {
        _this.$message.info('���雷图已经是最后一张了！')
      }
      _this.changeFengl()
    },
    // 上一张
    lastPic() {
      const _this = this
      const radarDate = new Date(_this.radarTime)
      const sateDate = new Date(_this.sateTime)
      if (_this.radarTime && radarDate >= sateDate) {
        if (_this.radarShow == 'QR' && _this.indexQR > 0) {
          _this.indexQR--
          _this.radarTime = _this.radarQRArr[_this.indexQR].time
        } else if (_this.radarShow == 'CR' && _this.indexCR > 0) {
          _this.indexCR--
          _this.radarTime = _this.radarCRArr[_this.indexCR].time
        } else {
          _this.$message.info('雷达图已经是第一张了！')
        }
      }
      if (_this.sateTime && radarDate <= sateDate) {
        if (_this.sateShow == 'fy01' && _this.indexfy01 > 0) {
          _this.indexfy01--
          _this.sateTime = _this.fy4a01Arr[_this.indexfy01].time
        } else if (_this.sateShow == 'fy09' && _this.indexfy09 > 0) {
          _this.indexfy09--
          _this.sateTime = _this.fy4a09Arr[_this.indexfy09].time
        } else if (_this.sateShow == 'fy12' && _this.indexfy12 > 0) {
          _this.indexfy12--
          _this.sateTime = _this.fy4a12Arr[_this.indexfy12].time
        } else if (_this.sateShow == 'B03' && _this.indexb03 > 0) {
          _this.indexb03--
          _this.sateTime = _this.b03Arr[_this.indexb03].time
        } else if (_this.sateShow == 'B08' && _this.indexb08 > 0) {
          _this.indexb08--
          _this.sateTime = _this.b08Arr[_this.indexb08].time
        } else if (_this.sateShow == 'B13' && _this.indexb13 > 0) {
          _this.indexb13--
          _this.sateTime = _this.b13Arr[_this.indexb13].time
        } else {
          _this.$message.info('卫星图已经是第一张了！')
        }
      }
      _this.changePic()
    },
    // 下一张
    nextPic() {
      const _this = this
      const radarDate = new Date(_this.radarTime)
      const sateDate = new Date(_this.sateTime)
      if (_this.radarTime && radarDate >= sateDate) {
        if (
          _this.radarShow == 'QR' &&
                    _this.indexQR < _this.radarQRArr.length - 1
        ) {
          _this.indexQR++
          _this.radarTime = _this.radarQRArr[_this.indexQR].time
        } else if (
          _this.radarShow == 'CR' &&
                    _this.indexCR < _this.radarCRArr.length - 1
        ) {
          _this.indexCR++
          _this.radarTime = _this.radarCRArr[_this.indexCR].time
        } else {
          _this.$message.info('雷达图已经是�����������张���！')
        }
      }
      if (_this.sateTime && radarDate >= sateDate) {
        if (
          _this.sateShow == 'fy01' &&
                    _this.indexfy01 < _this.fy4a01Arr.length - 1
        ) {
          _this.indexfy01++
          _this.sateTime = _this.fy4a01Arr[_this.indexfy01].time
        } else if (
          _this.sateShow == 'fy09' &&
                    _this.indexfy09 < _this.fy4a09Arr.length - 1
        ) {
          _this.indexfy09++
          _this.sateTime = _this.fy4a09Arr[_this.indexfy09].time
        } else if (
          _this.sateShow == 'fy12' &&
                    _this.indexfy12 < _this.fy4a12Arr.length - 1
        ) {
          _this.indexfy12++
          _this.sateTime = _this.fy4a12Arr[_this.indexfy12].time
        } else if (
          _this.sateShow == 'B03' &&
                    _this.indexb03 < _this.b03Arr.length - 1
        ) {
          _this.indexb03++
          _this.sateTime = _this.b03Arr[_this.indexb03].time
        } else if (
          _this.sateShow == 'B08' &&
                    _this.indexb08 < _this.b08Arr.length - 1
        ) {
          _this.indexb08++
          _this.sateTime = _this.b08Arr[_this.indexb08].time
        } else if (
          _this.sateShow == 'B13' &&
                    _this.indexb13 < _this.b13Arr.length - 1
        ) {
          _this.indexb13++
          _this.sateTime = _this.b13Arr[_this.indexb13].time
        } else {
          _this.$message.info('卫星图已经是最后一张���！')
        }
      }
      _this.changePic()
    },
    changePic() {
      const _this = this
      if (_this.radarTime) {
        if (_this.radarShow == 'CR') {
          _this.radarLayerCR.setSource(
            new ImageStatic({
              url: _this.radarCRArr[_this.indexCR].pic,
              imageExtent: _this.radarExtent,
              projection: 'EPSG:4326'
            })
          )
        } else if (_this.radarShow == 'QR') {
          _this.radarLayerQR.setSource(
            new ImageStatic({
              url: _this.radarQRArr[_this.indexQR].pic,
              imageExtent: _this.radarExtent,
              projection: 'EPSG:4326'
            })
          )
        }
      }
      if (_this.sateTime) {
        if (_this.sateShow == 'fy01') {
          _this.fy4a01Layer.setSource(
            new ImageStatic({
              url: _this.fy4a01Arr[_this.indexfy01].pic,
              imageExtent: _this.sateExtent,
              projection: 'EPSG:4326'
            })
          )
        } else if (_this.sateShow == 'fy09') {
          _this.fy4a09Layer.setSource(
            new ImageStatic({
              url: _this.fy4a09Arr[_this.indexfy09].pic,
              imageExtent: _this.sateExtent,
              projection: 'EPSG:4326'
            })
          )
        } else if (_this.sateShow == 'fy12') {
          _this.fy4a12Layer.setSource(
            new ImageStatic({
              url: _this.fy4a12Arr[_this.indexfy12].pic,
              imageExtent: _this.sateExtent,
              projection: 'EPSG:4326'
            })
          )
        } else if (_this.sateShow == 'B03') {
          _this.b03Layer.setSource(
            new ImageStatic({
              url: _this.b03Arr[_this.indexb03].pic,
              imageExtent: _this.sateExtent,
              projection: 'EPSG:4326'
            })
          )
        } else if (_this.sateShow == 'B08') {
          _this.b08Layer.setSource(
            new ImageStatic({
              url: _this.b08Arr[_this.indexb08].pic,
              imageExtent: _this.sateExtent,
              projection: 'EPSG:4326'
            })
          )
        } else if (_this.sateShow == 'B13') {
          _this.b13Layer.setSource(
            new ImageStatic({
              url: _this.b13Arr[_this.indexb13].pic,
              imageExtent: _this.sateExtent,
              projection: 'EPSG:4326'
            })
          )
        }
      }
    },
    changeStationTime(val) {
      const _this = this
      let buf = 0
      if (_this.dataCode === 'SURF_CHN_MUL_HOR') {
        buf = 60 * 60 * 1000
      } else if (_this.dataCode === 'SURF_CHN_MUL_MIN') {
        buf = 5 * 60 * 1000
      } else {
        buf = 5 * 60 * 1000
      }
      if (val === 'left') {
        _this.livingTime.setTime(_this.livingTime.getTime() - buf)
      } else {
        _this.livingTime.setTime(_this.livingTime.getTime() + buf)
      }
      _this.initLiveData()
      _this.initLightning()
    },
    changeStationEle(type, val) {
      const _this = this
      _this[`${type + 'Show'}`] = val
      for (
        let i = 0;
        i < _this.stationLayer.getLayers().getLength();
        i++
      ) {
        const layer = _this.stationLayer.getLayers().item(i)
        if (layer.getProperties().type === type) {
          layer.setVisible(val)
        }
      }
    },
    changeLightningEle(visible, val) {
      const _this = this
      if (parseInt(val) > 0) {
        _this.lightningHours = val
        _this.initLightning()
      }
      if (visible !== _this.showLightning) {
        _this.showLightning = visible
        for (
          let i = 0;
          i < _this.lightningLayer.getLayers().getLength();
          i++
        ) {
          const layer = _this.lightningLayer.getLayers().item(i)
          layer.setVisible(visible)
        }
      }
    },

    initLiveData() {
      const _this = this
      if (_this.dataCode === 'PRE') {
        _this.getPreLivingData()
      } else {
        _this.getLivingData()
      }
      _this.stationLayer.set('dataType', _this.dataType)
      _this.stationLayer.set('data', _this.livingData)

      _this.stationLayer.getLayers().forEach(function(feature) {
        feature.setStyle(_this.stationFeatureStyle())
      })

      _this.map.addEventListener('singleclick', function(e) {
        // if (!_plot.drawFlag) {
        // 只弹出一个
        let popupLayerShown = false
        _this.map.forEachFeatureAtPixel(e.pixel, function(
          feature,
          layer
        ) {
          if (!popupLayerShown && feature.name === 'station') {
            popupLayerShown = true
            _this.clickStationId = feature.refData.Station_Id_C
            _this.stationVisible = true
          }
        })
        // }
      })
    },

    initLightning() {
      const _this = this
      _this.getLightningData()
      _this.lightningLayer.set('data', _this.lightningData)

      _this.lightningLayer.getLayers().forEach(function(feature) {
        feature.setStyle(_this.lightningFeatureStyle())
      })
    },
    reloadThresholdData() {
      const me = this
      return new Promise(function(sec, rej) {
        const areaName = '巴彦淖尔市'

        const res = [
          {
            id: 2661,
            Condition: '(TEM_Min<-25)',
            Name: '最低气温小于-20摄氏度',
            Cnty: '五原县',
            Statement: '最低气温小于-25���',
            beSound: true,
            beTwinkle: true,
            beMsg: true,
            Status: '未处理',
            nextDTime: '2020-05-02 10:09:45',
            is_forecast: false,
            aging: '0-24'
          }
        ]
        // me.axios.get({
        //   url:me.VUE_APP_BASE_API+"/api/threshold/"+areaName,
        // }).then(res => {
        me.thresholdArr = res.filter(t => !t.is_forecast)
        me.forecastThresholdArr = res.filter(t => t.is_forecast)
        me.thresholdArr.forEach(t => {
          me.thresholdCache[t.id] = t
        })
        me.forecastThresholdArr.forEach(t => {
          me.forecastThresholdCache[t.id] = t
        })
        sec()
        // })
      })
    },
    stationFeatureStyle() {
      // let zoom = Math.floor(this.map.getView().getZoom()) - 4;
      // let scale = zoom < 4 ? resolutions[4] / resolutions[zoom] : 0.8;
      const scale = 0.8
      const _this = this
      return function(feature) {
        if (feature.attachData.value === 'ddff') {
          if (feature.formattedValue.level > 20) {
            return
          }
          return new Style({
            image: new Icon({
              src: require('@/assets/images/windSymbol/wind' +
                                feature.formattedValue.level +
                                '.png'),
              rotation: feature.formattedValue.radDirect,
              scale: 0.8 * scale
            })
          })
        } else if (
          feature.attachData.value === 'SUM_RR' ||
                    feature.attachData.value === 'MIN_RR'
        ) {
          console.log(feature.attachData.value)
          return new Style({
            image: new Icon({
              src: require('@/assets/images/lightning.png'),
              scale: 0.8 * scale
            })
          })
        } else if (feature.attachData.value === 'WW') {
          if (
            feature.formattedValue !== '0' &&
                        feature.formattedValue !== '999'
          ) {
            return new Style({
              image: new Icon({
                src: require('@/assets/images/weather/' +
                                    _this.getWeatherDescByCode(
                                      feature.formattedValue
                                    ) +
                                    '.png'),
                scale: 0.08
              })
            })
          }
        } else if (feature.attachData.value === 'X') {
          if (
            feature.formattedValue < 14 &&
                        (feature.refData['WEP_Now'] === undefined ||
                            feature.refData['WEP_Now'] === '0' ||
                            feature.refData['WEP_Now'] === '999')
          ) {
            return new Style({
              image: new Circle({
                fill: new Fill({
                  color: '#e6a23c'
                }),
                stroke: new Stroke({
                  color: '#000000',
                  width: 1
                }),
                radius: 6 * scale
              })
            })
          } else if (feature.formattedValue > 13) {
            return new Style({
              image: new Circle({
                fill: new Fill({
                  color: '#2be446'
                }),
                stroke: new Stroke({
                  color: '#000000',
                  width: 1
                }),
                radius: 5 * scale
              })
            })
          }
        } else if (
          feature.attachData.value === 'SUM_RR' ||
                    feature.attachData.value === 'MIN_RR' ||
                    feature.attachData.value === 'RR'
        ) {
          return new Style({
            text: new Text({
              font: '23px Microsoft YaHei',
              text: feature.formattedValue,
              fill: new Fill({
                color: feature.attachData.color
              }),
              scale: scale,
              offsetX: 30 * scale * feature.attachData.x,
              offsetY: 30 * scale * feature.attachData.y
            })
          })
        } else {
          return new Style({
            text: new Text({
              font: '15px Microsoft YaHei',
              text: feature.formattedValue,
              fill: new Fill({
                color: feature.attachData.color
              }),
              scale: scale,
              offsetX: 30 * scale * feature.attachData.x,
              offsetY: 30 * scale * feature.attachData.y
            })
          })
        }
      }
    },

    lightningFeatureStyle() {
      // let zoom = Math.floor(this.map.getView().getZoom()) - 4;
      // let scale = zoom < 4 ? resolutions[4] / resolutions[zoom] : 0.8;
      const scale = 1
      return function(feature) {
        return new Style({
          image: new Icon({
            src: require('@/assets/images/lightning.png'),
            scale: 0.15 * scale
          })
        })
      }
    },
    // 初始化雷达数据
    initRadarData() {
      const _this = this
      lastRadar().then(res => {
        if (res.code === 200) {
          _this.radarCRArr = []
          _this.radarQRArr = []
          for (let i = 0; i < res.data.CR.imgs.length; i++) {
            _this.radarCRArr.push({
              pic: res.data.CR.imgs[i],
              time: res.data.CR.times[i]
            })
            _this.radarQRArr.push({
              pic: res.data.QR.imgs[i],
              time: res.data.QR.times[i]
            })
          }
          _this.indexQR = _this.radarQRArr.length - 1
          _this.indexCR = _this.radarCRArr.length - 1
          if (_this.radarShow === 'QR') {
            _this.radarChanged('layerQR')
          } else if (_this.radarShow === 'CR') {
            _this.radarChanged('layerCR')
          }
          // for (let index = 0; index < _this.radarQRArr.length; index++) {
          //   _this.radarTime = _this.radarQRArr[index].time;
          //   _this.indexQR = index;
          //   _this.changePic();
          //   _this.sleep(500);
          // }
        }
      })
    },
    // 初始化风雷数据
    initFengl() {
      const _this = this
      lastRadarTime().then(res => {
        if (res.code === 200) {
          _this.fenglArr = []
          const lastTime = res.data
          // 将lastTime转���为Date对象
          const baseDate = new Date(
            lastTime.slice(0, 4), // 年
            lastTime.slice(4, 6) - 1, // 月（注意要减1）
            lastTime.slice(6, 8), // 日
            parseInt(lastTime.slice(8, 10)) + 8, // 时（世���时转北京时）
            lastTime.slice(10, 12) // 分
          )

          for (let i = 6; i <= 180; i = i + 6) {
            // 创建新的Date对象并加上i分钟
            const newDate = new Date(baseDate.getTime() + i * 60000)

            // 格式化新的时间字符串
            const newTimeStr = newDate.getFullYear() +
                            (newDate.getMonth() + 1).toString().padStart(2, '0') +
                            newDate.getDate().toString().padStart(2, '0') +
                            newDate.getHours().toString().padStart(2, '0') +
                            newDate.getMinutes().toString().padStart(2, '0')

            // 格式化i为三位数字字符串
            const formattedI = i.toString().padStart(3, '0')

            _this.fenglArr.push({
              pic: '/static2/images/fengl/' + lastTime + '-' + formattedI + '.png',
              time: newDate.getFullYear() + '-' +
                                  (newDate.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                  newDate.getDate().toString().padStart(2, '0') + ' ' +
                                  newDate.getHours().toString().padStart(2, '0') + ':' +
                                  newDate.getMinutes().toString().padStart(2, '0')
            })
          }
          _this.changeFengl()
        }
      })
    },
    switchFengl(val) {
      this.fenglShow = val
      this.fenglLayerCR.setVisible(val)
    },
    changeFengl() {
      const _this = this
      _this.fenglLayerCR.setVisible(false)
      _this.fenglTime = false
      _this.fenglShow = ''
      if (_this.fenglArr.length > 0) {
        if (_this.indexFengl === -1) {
          _this.indexFengl = _this.fenglArr.length - 1
        }
        _this.fenglLayerCR.setSource(
          new ImageStatic({
            url: _this.fenglArr[_this.indexFengl].pic,
            imageExtent: _this.fenglExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.fenglShow = true
        _this.fenglTime = _this.fenglArr[_this.indexFengl].time
        _this.fenglLayerCR.setVisible(true)
      }
    },
    flushRadarData() {
      const _this = this
      lastRadar()
        .then(res => {
          if (res.code === 200) {
            _this.radarCRArr = []
            _this.radarQRArr = []
            for (let i = 0; i < res.data.CR.imgs.length; i++) {
              _this.radarCRArr.push({
                pic: res.data.CR.imgs[i],
                time: res.data.CR.times[i]
              })
              _this.radarQRArr.push({
                pic: res.data.QR.imgs[i],
                time: res.data.QR.times[i]
              })
            }
            _this.indexQR = _this.radarQRArr.length - 1
            _this.indexCR = _this.radarCRArr.length - 1
            if (_this.radarShow === 'QR') {
              _this.radarChanged('layerQR')
            } else if (_this.radarShow === 'CR') {
              _this.radarChanged('layerCR')
            }
          } else {
          }
        })
        .catch(err => { })
    },
    radarChanged(layerName) {
      const _this = this
      _this.radarLayerQR.setVisible(false)
      _this.radarLayerCR.setVisible(false)
      _this.radarTime = false
      _this.sateShow = ''
      if (layerName === 'layerQR' && _this.radarQRArr.length > 0) {
        if (_this.indexQR === -1) {
          _this.indexQR = _this.radarQRArr.length - 1
        }
        _this.radarLayerQR.setSource(
          new ImageStatic({
            url: _this.radarQRArr[_this.indexQR].pic,
            imageExtent: _this.radarExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.radarShow = 'QR'
        _this.radarTime = _this.radarQRArr[_this.indexQR].time
        _this.radarLayerQR.setVisible(true)
      } else if (layerName === 'layerCR' && _this.radarCRArr.length > 0) {
        if (_this.indexCR === -1) {
          _this.indexCR = _this.radarCRArr.length - 1
        }
        _this.radarLayerCR.setSource(
          new ImageStatic({
            url: _this.radarCRArr[_this.indexCR].pic,
            imageExtent: _this.radarExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.radarShow = 'CR'
        _this.radarTime = _this.radarCRArr[_this.indexQR].time
        _this.radarLayerCR.setVisible(true)
      } else {
        _this.radarShow = false
      }
    },
    changeCLDAS(element, val) {
      const _this = this
      // 清除图层
      _this.liveGridLayer.getLayers().forEach(function(layer) {
        layer.setVisible(val)
      })
      if (_this.liveGridElement === element && _this.liveGridTime === _this.livingTime) {
        return
      }
      if (!val) {
        return
      }

      const params = {
        element: element,
        ymdh: _this.livingTime,
        type: 'live'
      }
      getLiveGrid(params).then(res => {
        if (res.code === 200) {
          const gridGeoJson = res.data.geoJson
          gridGeoJson.type = 'FeatureCollection'
          const format = new GeoJSON()
          const featureslines = format.readFeatures(gridGeoJson, {
            featureProjection: 'EPSG:3857',
            dataProjection: 'EPSG:4326'
          })
          _this.liveGridLayer.getLayers().item(0).getSource().addFeatures(featureslines)
          _this.liveGridLayer.getLayers().item(0).setProperties({
            'element': element,
            'aging': 1,
            'ymdh': _this.livingTime
            // "image": res.image
          })
          _this.liveGridLayer.getLayers().item(1).setProperties({
            'res': res.data
          })
          _this.reloadGridPoint()
          _this.liveGridTime = _this.livingTime
          _this.liveGridElement = element
        }
      }
      )
    },
    reloadGridPoint() {
      const _this = this
      const markers = []
      const res = _this.liveGridLayer.getLayers().item(1).getProperties('res').res
      // 左下角的点，x较小，y较大
      const minXY = _this.map.getPixelFromCoordinate([res.lonMin, res.latMin])
      // 右上角的点，x较大，y较小
      const maxXY = _this.map.getPixelFromCoordinate([res.lonMax, res.latMax])
      let pixelGap = (maxXY[0] - minXY[0]) / res.data.datas[0].gridData.length
      pixelGap = pixelGap < 0.0008 ? 0.0008 : pixelGap
      const gridData = res.data.datas[0].gridData
      for (let x = minXY[0]; x <= maxXY[0]; x += pixelGap) {
        for (let y = maxXY[1]; y <= minXY[1]; y += pixelGap) {
          const xIndex = Math.floor((x - minXY[0]) / (maxXY[0] - minXY[0]) * gridData[0].length)
          const yIndex = Math.floor((y - maxXY[1]) / (minXY[1] - maxXY[1]) * gridData.length)

          const marker = new Feature({
            geometry: new Point(
              _this.map.getCoordinateFromPixel([x, y])
            ).transform('EPSG:4326', 'EPSG:3857')
          })
          // TODO 可能越界
          // if (yIndex >= gridData.length) {
          //     // console.log(yIndex + "," + gridData.length);
          // }
          marker.setProperties({
            element: res.data.element,
            gridValue: gridData[yIndex][xIndex]
          })
          // marker = marker.getGeometry().clone().transform(_this.map.getView().getProjection(),'EPSG:4326')
          markers.push(marker)
        }
      }
      _this.liveGridLayer.getLayers().item(1).getSource().clear()
      _this.liveGridLayer.getLayers().item(1).getSource().addFeatures(markers)
    },
    sateChanged(layerName) {
      const _this = this
      _this.fy4a01Layer.setVisible(false)
      _this.fy4a09Layer.setVisible(false)
      _this.fy4a12Layer.setVisible(false)
      _this.b03Layer.setVisible(false)
      _this.b08Layer.setVisible(false)
      _this.b13Layer.setVisible(false)
      _this.sateTime = false
      _this.sateShow = ''
      if (layerName === 'FY4a01' && _this.fy4a01Arr.length > 0) {
        if (_this.indexfy01 === -1) {
          _this.indexfy01 = _this.fy4a01Arr.length - 1
        }
        _this.fy4a01Layer.setSource(
          new ImageStatic({
            url: _this.fy4a01Arr[_this.indexfy01].pic,
            imageExtent: _this.sateExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.sateShow = 'fy01'
        _this.sateTime = _this.fy4a01Arr[_this.indexfy01].time
        _this.fy4a01Layer.setVisible(true)
      } else if (layerName === 'FY4a09' && _this.fy4a09Arr.length > 0) {
        if (_this.indexfy09 === -1) {
          _this.indexfy09 = _this.fy4a09Arr.length - 1
        }
        _this.fy4a09Layer.setSource(
          new ImageStatic({
            url: _this.fy4a09Arr[_this.indexfy09].pic,
            imageExtent: _this.sateExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.sateShow = 'fy09'
        _this.sateTime = _this.fy4a09Arr[_this.indexfy09].time
        _this.fy4a09Layer.setVisible(true)
      } else if (layerName === 'FY4a12' && _this.fy4a12Arr.length > 0) {
        if (_this.indexfy12 === -1) {
          _this.indexfy12 = _this.fy4a12Arr.length - 1
        }
        _this.fy4a12Layer.setSource(
          new ImageStatic({
            url: _this.fy4a12Arr[_this.indexfy12].pic,
            imageExtent: _this.sateExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.sateShow = 'fy12'
        _this.sateTime = _this.fy4a12Arr[_this.indexfy12].time
        _this.fy4a12Layer.setVisible(true)
      } else if (layerName === 'B03' && _this.b03Arr.length > 0) {
        if (_this.indexb03 === -1) {
          _this.indexb03 = _this.b03Arr.length - 1
        }
        _this.b03Layer.setSource(
          new ImageStatic({
            url: _this.b03Arr[_this.indexb03].pic,
            imageExtent: _this.sateExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.sateShow = 'b03'
        _this.sateTime = _this.b03Arr[_this.indexb03].time
        _this.b03Layer.setVisible(true)
      } else if (layerName === 'B08' && _this.b08Arr.length > 0) {
        if (_this.indexb08 === -1) {
          _this.indexb08 = _this.b08Arr.length - 1
        }
        _this.b08Layer.setSource(
          new ImageStatic({
            url: _this.b08Arr[_this.indexb08].pic,
            imageExtent: _this.sateExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.sateShow = 'b08'
        _this.sateTime = _this.b08Arr[_this.indexb08].time
        _this.b08Layer.setVisible(true)
      } else if (layerName === 'B13' && _this.b13Arr.length > 0) {
        if (_this.indexb13 === -1) {
          _this.indexb13 = _this.b13Arr.length - 1
        }
        _this.b13Layer.setSource(
          new ImageStatic({
            url: _this.b13Arr[_this.indexb13].pic,
            imageExtent: _this.sateExtent,
            projection: 'EPSG:4326'
          })
        )
        _this.sateShow = 'b13'
        _this.sateTime = _this.b13Arr[_this.indexb13].time
        _this.b13Layer.setVisible(true)
      }
    },

    // window.radarArr = [];
    // res.time.reverse();
    // res.DS.reverse().forEach((i, index) => {
    //   window.radarArr.push({
    //     pic: i,
    //     time: res.time[index],
    //   });
    // });
    // }).catch(e => {
    //   console.log("error:" + e)
    // });
    // 初始化卫星数据
    initSateData() {
      const _this = this
      lastSate()
        .then(res => {
          if (res.code === 200) {
            _this.fy4a01Arr = []
            _this.fy4a09Arr = []
            _this.fy4a12Arr = []
            _this.b03Arr = []
            _this.b08Arr = []
            _this.b13Arr = []
            let data = res.data.C001
            for (let i = 0; i < data.imgs.length; i++) {
              _this.fy4a01Arr.push({
                pic: data.imgs[i],
                time: data.times[i]
              })
            }
            data = res.data.C009
            for (let i = 0; i < data.imgs.length; i++) {
              _this.fy4a09Arr.push({
                pic: data.imgs[i],
                time: data.times[i]
              })
            }
            data = res.data.C012
            for (let i = 0; i < data.imgs.length; i++) {
              _this.fy4a12Arr.push({
                pic: data.imgs[i],
                time: data.times[i]
              })
            }
            data = res.data.B03
            for (let i = 0; i < data.imgs.length; i++) {
              _this.b03Arr.push({
                pic: data.imgs[i],
                time: data.times[i]
              })
            }
            data = res.data.B08
            for (let i = 0; i < data.imgs.length; i++) {
              _this.b08Arr.push({
                pic: data.imgs[i],
                time: data.times[i]
              })
            }
            data = res.data.B13
            for (let i = 0; i < data.imgs.length; i++) {
              _this.b13Arr.push({
                pic: data.imgs[i],
                time: data.times[i]
              })
            }
          } else {
          }
        })
        .catch(err => { })
    },
    // 实况数据
    reloadLiveData() {
      const _this = this
      // 清除图层
      _this.stationLayer.getLayers().forEach(function(layer) {
        layer.getSource().clear()
      })
      for (let i = 0; i < _this.livingData.length; i++) {
        const data = _this.livingData[i]
        if (!this.livingFeatureCache[data.Station_Id_C]) {
          this.livingFeatureCache[data.Station_Id_C] = true
        }
        const latlon = [parseFloat(data.Lon), parseFloat(data.Lat)]
        const coor = fromLonLat(latlon)

        outerloop: for (let j = 0; j < attachDatas.length; j++) {
          if (attachDatas[j].dataCode.indexOf(_this.dataCode) < 0) {
            continue
          }
          let formattedValue
          if (attachDatas[j].element instanceof Array) {
            formattedValue = []
            for (
              let k = 0;
              k < attachDatas[j].element.length;
              k++
            ) {
              const elementValue = data[attachDatas[j].element[k]]
              if (!elementValue) {
                continue outerloop
              }
              formattedValue.push(elementValue)
            }
          } else {
            formattedValue = data[attachDatas[j].element]
            if (attachDatas[j].value === 'X') {
              formattedValue = data['Station_levl']
            }
            if (!formattedValue) {
              continue
            }
          }
          if (attachDatas[j].formatter) {
            formattedValue = attachDatas[j].formatter(
              formattedValue
            )
          }
          // 非法数据显示空内容
          if (formattedValue > 99999) {
            formattedValue = '999'
          }
          const feature = new Feature({
            geometry: new Point(coor)
          })
          feature.formattedValue = formattedValue
          feature.attachData = attachDatas[j]
          if (
            attachDatas[j].value === 'WW' ||
                        attachDatas[j].value === 'X' ||
                        attachDatas[j].value === 'SUM_RR' ||
                        attachDatas[j].value === 'MIN_RR'
          ) {
            feature.name = 'station'
            feature.refData = data
          }
          _this.stationLayer
            .getLayers()
            .item(j)
            .getSource()
            .addFeature(feature)
        }
      }
    },
    reloadLightning() {
      const _this = this
      // 清除图层
      _this.lightningLayer.getLayers().forEach(function(layer) {
        layer.getSource().clear()
      })
      for (let i = 0; i < _this.lightningData.length; i++) {
        const data = _this.lightningData[i]

        const latlon = [parseFloat(data.Lon), parseFloat(data.Lat)]
        const coor = fromLonLat(latlon)

        const feature = new Feature({
          geometry: new Point(coor)
        })
        feature.name = 'lightning'
        feature.refData = data
        _this.lightningLayer
          .getLayers()
          .item(0)
          .getSource()
          .addFeature(feature)
      }
    },
    flushChange(value) {
      console.log(value)
      if (value) {
        this.livingTime = new Date()
        this.initLiveData()
        this.flushRadarData()
        this.initSateData()
        this.initLightning()
        this.thresholdData()
        this.flushTimer = setInterval(() => {
          this.livingTime = new Date()
          this.initLiveData()
          this.flushRadarData()
          this.initSateData()
          this.initLightning()
          this.thresholdData()
        }, 1000 * 60 * 10)
      } else {
        clearInterval(this.flushTimer)
        this.flushTimer = null
      }
    },
    getElementLabel(code) {
      return this.elementLabels[code] || code
    },
    handleDialogOpen() {
      // 先获取数据
      this.getThreshold()

      // 使用 nextTick 确保 DOM 更新后再执行
      this.$nextTick(() => {
        // 强制更新组件
        this.$forceUpdate()
        // 手动触发一次窗口resize事件
        window.dispatchEvent(new Event('resize'))
      })
    },
    formatStationName(name) {
      if (!name) return ''
      return name.length > 4 ? name.substring(0, 4) + '...' : name
    },
    handleRowClick(row) {
      // 清除之前的边框
      if (this.highlightLayer) {
        this.map.removeLayer(this.highlightLayer)
      }

      // 创建站点坐标点
      const coord = fromLonLat([parseFloat(row.Lon), parseFloat(row.Lat)])

      // 初始边框范围（大）
      const initialExtent = [
        coord[0] - 30000,
        coord[1] - 30000,
        coord[0] + 30000,
        coord[1] + 30000
      ]

      // 最终边框范围（小）
      const finalExtent = [
        coord[0] - 10000,
        coord[1] - 10000,
        coord[0] + 10000,
        coord[1] + 10000
      ]

      // 创建四个角的样式
      const createCornerStyle = (extent) => {
        const cornerLength = 3000 // 角线段的长度
        return new Style({
          stroke: new Stroke({
            color: '#ff0000',
            width: 3
          }),
          geometry: new MultiLineString([
            // 左上角 (两条线段: 竖线和横线)
            [[extent[0], extent[3]], [extent[0], extent[3] - cornerLength]], // 竖线
            [[extent[0], extent[3]], [extent[0] + cornerLength, extent[3]]], // 横线

            // 右上角 (两条线段)
            [[extent[2], extent[3]], [extent[2], extent[3] - cornerLength]], // 竖线
            [[extent[2], extent[3]], [extent[2] - cornerLength, extent[3]]], // 横线

            // 左下角 (两条线段)
            [[extent[0], extent[1]], [extent[0], extent[1] + cornerLength]], // 竖线
            [[extent[0], extent[1]], [extent[0] + cornerLength, extent[1]]], // 横线

            // 右下角 (两条线段)
            [[extent[2], extent[1]], [extent[2], extent[1] + cornerLength]], // 竖线
            [[extent[2], extent[1]], [extent[2] - cornerLength, extent[1]]] // 横线
          ])
        })
      }

      // 创建矩形要素（仅用于定位，不显示）
      const feature = new Feature({
        geometry: new Polygon([[
          [initialExtent[0], initialExtent[1]],
          [initialExtent[2], initialExtent[1]],
          [initialExtent[2], initialExtent[3]],
          [initialExtent[0], initialExtent[3]],
          [initialExtent[0], initialExtent[1]]
        ]])
      })

      // 创建新图层
      this.highlightLayer = new LayerVector({
        source: new SourceVector({
          features: [feature]
        }),
        style: createCornerStyle(initialExtent),
        zIndex: 999
      })

      // 添加图层到地图
      this.map.addLayer(this.highlightLayer)

      // 动画效果
      const duration = 1000 // 动画持续时间
      const start = Date.now()

      const animate = () => {
        const elapsed = Date.now() - start
        const progress = Math.min(elapsed / duration, 1)

        // 计算当前边框范围
        const currentExtent = [
          initialExtent[0] + (finalExtent[0] - initialExtent[0]) * progress,
          initialExtent[1] + (finalExtent[1] - initialExtent[1]) * progress,
          initialExtent[2] + (finalExtent[2] - initialExtent[2]) * progress,
          initialExtent[3] + (finalExtent[3] - initialExtent[3]) * progress
        ]

        // 更新样式
        this.highlightLayer.setStyle(createCornerStyle(currentExtent))

        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }

      animate()
    },
    // 添加一个方法来处理用户交互
    handleUserInteraction() {
      // 在用户第一次交互时初始化音频
      if (!this.audioInitialized) {
        this.initAudio()
      }
      // 如果 AudioContext 已暂停，则恢复它
      if (this.audioContext && this.audioContext.state === 'suspended') {
        this.audioContext.resume()
      }
      this.$emit('user-interaction')
    },
    // 初始化音频
    async initAudio() {
      if (this.audioInitialized) return

      try {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
        const response = await fetch(require('@/assets/warning.wav'))
        const arrayBuffer = await response.arrayBuffer()
        this.warningBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
        this.audioInitialized = true
      } catch (e) {
        console.log('Audio init failed:', e)
      }
    },
    // 播放警告音
    async playWarningSound() {
      // 如果还没初始化音频，先初始化
      if (!this.audioInitialized) {
        this.initAudio()
      }

      if (this.audioContext && this.warningBuffer && !this.hasPlayedWarning) {
        try {
          // 确保 AudioContext 处于运行状态
          if (this.audioContext.state === 'suspended') {
            this.audioContext.resume()
          }

          const source = this.audioContext.createBufferSource()
          source.buffer = this.warningBuffer
          source.connect(this.audioContext.destination)
          source.start(0)

          this.hasPlayedWarning = true
          setTimeout(() => {
            this.hasPlayedWarning = false
          }, 5 * 60 * 1000)
        } catch (e) {
          console.log('Play warning failed:', e)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-radio-button--medium .el-radio-button__inner {
    padding: 10px;
}

>>>.el-radio {
    margin-right: 9px;
}

.leftRightPic {
    position: fixed;
    top: calc(50vh - 17px);
    height: 109px;
    width: 50px;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;
}

.radarsateWrap {
    z-index: 9;
    position: absolute;
    bottom: 70px;
    color: #0a89e1;
    background: rgba(230, 247, 255, 0.8);
    font-size: 14px;
}

.stationPanel {
    z-index: 2002;
    display: inline;
    position: absolute;
    top: 84px;
    font-size: 20px;

    >>>.el-switch {
        height: 15px !important;
        line-height: 15px !important;
        margin-bottom: 8px;
        margin-right: 15px;
    }

    >>>.el-switch__core {
        height: 15px !important;

    }

    >>>.el-switch__label {
        color: #fff !important;
        height: 15px !important;
        line-height: 15px !important;
    }

    >>>.el-switch__label * {
        font-size: 14px !important;
    }

    >>>.el-switch__core:after {
        width: 14px !important;
        height: 14px !important;
        top: 0 !important;
        left: 0 !important;
    }

    >>>.el-switch.is-checked .el-switch__core::after {
        left: 100% !important;
        margin-left: -15px;

    }

    >>>.el-switch__label.is-active {
        color: #a4ffff !important;
    }

    .radio_list {
        width: 192px;
        height: 30px;
        background: rgba(19, 60, 95, 0.65);
        border-radius: 3px;
        margin-bottom: 8px;
        padding: 0 4px;
        display: flex;
        align-items: center;

        &>>>.el-radio-button {
            width: 64px;
            height: 23px;
        }

        &>>>.el-radio-button is-active {
            border: none !important;
        }

        &>>>.el-radio-button__inner {
            width: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            font-size: 14px;
            height: 23px;
            background: transparent;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #d0deee;
            box-shadow: none !important;
        }

        &>>>.el-radio-button__orig-radio:checked+.el-radio-button__inner {
            background: #a4ffff;
            border-radius: 3px;
            color: #133a5d;
            border: none !important;
        }
    }
}

.ttt {
    color: red;
}

.ol-popup {
    position: absolute;
    background-color: white;
    -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #cccccc;
    bottom: 12px;
    left: -50px;
    min-width: 200px;
    font-size: 14px;
}

.ol-popup:after,
.ol-popup:before {
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.ol-popup:after {
    border-top-color: white;
    border-width: 10px;
    left: 48px;
    margin-left: -10px;
}

.ol-popup:before {
    border-top-color: #cccccc;
    border-width: 11px;
    left: 48px;
    margin-left: -11px;
}

#mouse-position>>>.custom-mouse-position {
    color: #000;
    font-size: 12px;
}

#map {
    // position: absolute;
    // top: 60px;
    // left: 0;
    width: 100vw;
    // height: calc(100vh - 70px);
    height: 100vh;
}

.base-container {
    &>.el-dialog__body {
        padding: 0;
        background: rgb(241, 246, 252);
    }

    &>.el-date-editor--datetimerange.el-input,
    &>.el-date-editor--datetimerange.el-input__inner {
        width: 220px;
    }

    .data-memo>.el-col {
        color: #2c7fc8;
        font-size: 12px;
        line-height: 20px;
    }

    &>.el-form--inline .el-form-item {
        width: 100%;

        .el-form-item__content {
            width: 85%;

            .el-select,
            .el-date-editor.el-range-editor.el-input__inner.el-date-editor--datetimerange {
                width: 100%;
            }
        }
    }

    .submit {
        width: 100%;

        &>.el-form-item__content,
        &>button.el-button.el-button--primary {
            width: 100% !important;
        }
    }
}

.form-inline {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-end;
    align-items: center;

    .form-group {
        display: flex;
        align-items: center;

        span {
            margin: 0 5px;
        }
    }

    input {
        height: 36px;
        font-size: 14px;
        border: 1px solid #ccc;
        padding: 0 10px;
    }

    .btn-search {
        height: 36px;
        background: #247cf4;
        border-radius: 3px;
        color: #fff;
        padding: 5px 10px;
        font-size: 12px;
        border: 0;
        outline: none;
        margin-left: 10px;
    }

    #stationTableLayer>>>.el-range-editor .el-range-input {
        width: 130px;
    }

    .baseLayerChange {
        margin: 0 10px;
        display: flex;
        flex-flow: column wrap;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        span {
            margin-top: 8px;
            line-height: 20px;
            font-size: 14px;
            font-family: PingFang SC;
            font-weight: bold;
            color: #333;
        }

        &.active img {
            border: 2px solid #247cf4;
            width: 100px;
            height: 80px;
        }

        &.active span {
            color: #247cf4;
        }
    }

    .lastPic {
        position: fixed;
        top: calc(70vh - 17px);
        left: 0px;
        background-image: url("../assets/images/left.svg");
        height: 30px;
        width: 34px;
        cursor: pointer;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 100% 100%;
        z-index: 1001;
    }

    .nextPic {
        position: fixed;
        top: calc(70vh - 17px);
        right: 0px;
        background-image: url("../assets/images/right.svg");
        height: 30px;
        width: 34px;
        cursor: pointer;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 100% 100%;
        z-index: 1001;
    }
}

.threshold-panel {
    position: absolute;
    top: 120px;
    width: 280px;
    background: rgba(19, 60, 95, 0.85);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
    z-index: 1000;

    .panel-header {
        padding: 10px;
        border-bottom: 1px solid rgba(208, 222, 238, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
            font-size: 14px;
            font-weight: bold;
            color: #d0deee;
        }

        .close-btn {
            padding: 0;
            color: #d0deee;
        }
    }

    .panel-content {
        padding: 5px;
        max-height: 380px;
        overflow-y: auto;
    }

    ::v-deep .el-table {
        background-color: rgba(19, 60, 95, 0.65) !important;  // 修改整体背景色

        &::before,
        &::after {
            display: none;
        }

        th {
            background-color: rgba(19, 60, 95, 0.85) !important;  // 修改表头背景色
            color: #d0deee !important;
            border-bottom: 1px solid rgba(208, 222, 238, 0.2) !important;
            padding: 8px 0 !important;
        }

        td {
            background-color: rgba(19, 60, 95, 0.65) !important;  // 修改单元格背景色
            color: #d0deee !important;
            border-bottom: 1px solid rgba(208, 222, 238, 0.1) !important;
            padding: 8px 0 !important;
        }

        tr {
            background-color: transparent !important;
        }

        &, tr, th, td {
            border-color: rgba(208, 222, 238, 0.1) !important;
        }

        tr:hover > td {
            background-color: rgba(208, 222, 238, 0.15) !important;
        }

        .cell {
            padding: 0 5px !important;
        }

        // 修改表格内的文字颜色
        .el-table__row {
            color: #d0deee !important;
        }
    }
}
</style>
